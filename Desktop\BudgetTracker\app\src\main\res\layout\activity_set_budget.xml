<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/theme_background"
    tools:context=".SetBudgetActivity">

    <!-- Header -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="Back"
            app:tint="@color/light_green" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/montserrat_bold"
            android:text="Set Monthly Budget"
            android:textColor="@color/light_green"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/save_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/tick_bg"
            android:fontFamily="@font/montserrat_bold"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:text="Save"
            android:textColor="@color/light_green"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instructions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/header"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:fontFamily="@font/montserrat_regular"
        android:text="Set your monthly spending limit for each category. Leave blank to skip."
        android:textColor="@color/light_green"
        android:textSize="14sp" />

    <!-- Budget Setup RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/budget_setup_rv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/instructions"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp" />

</RelativeLayout>
