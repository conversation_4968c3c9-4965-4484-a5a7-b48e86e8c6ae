package com.arijit.budgettracker.utils

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.arijit.budgettracker.R
import com.arijit.budgettracker.models.BudgetProgress

class BudgetProgressAdapter : ListAdapter<BudgetProgress, BudgetProgressAdapter.BudgetProgressViewHolder>(BudgetProgressDiffCallback()) {

    var onItemClick: ((BudgetProgress) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BudgetProgressViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_budget_progress, parent, false)
        return BudgetProgressViewHolder(view)
    }

    override fun onBindViewHolder(holder: BudgetProgressViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class BudgetProgressViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val categoryText: TextView = itemView.findViewById(R.id.category_text)
        private val budgetAmountText: TextView = itemView.findViewById(R.id.budget_amount_text)
        private val spentAmountText: TextView = itemView.findViewById(R.id.spent_amount_text)
        private val progressBar: ProgressBar = itemView.findViewById(R.id.budget_progress_bar)
        private val progressPercentageText: TextView = itemView.findViewById(R.id.progress_percentage_text)
        private val cardView: CardView = itemView.findViewById(R.id.budget_progress_card)

        fun bind(budgetProgress: BudgetProgress) {
            val currencySymbol = CurrencyPrefs.getSymbol(itemView.context)

            categoryText.text = budgetProgress.category

            // Show spent / budget format
            spentAmountText.text = "$currencySymbol${budgetProgress.spentAmount.toInt()} / $currencySymbol${budgetProgress.budgetAmount.toInt()}"

            // Show remaining budget or overspent amount
            val remaining = budgetProgress.budgetAmount - budgetProgress.spentAmount
            if (remaining >= 0) {
                budgetAmountText.text = "Remaining: $currencySymbol${remaining.toInt()}"
                budgetAmountText.setTextColor(itemView.context.getColor(R.color.medium_green))
            } else {
                budgetAmountText.text = "Over by: $currencySymbol${(-remaining).toInt()}"
                budgetAmountText.setTextColor(itemView.context.getColor(android.R.color.holo_red_dark))
            }

            progressBar.progress = budgetProgress.progressPercentage
            progressPercentageText.text = "${budgetProgress.progressPercentage}%"

            // Change colors based on budget status
            if (budgetProgress.isOverBudget) {
                progressBar.progressTintList = itemView.context.getColorStateList(android.R.color.holo_red_dark)
                progressPercentageText.setTextColor(itemView.context.getColor(android.R.color.holo_red_dark))
            } else if (budgetProgress.progressPercentage > 80) {
                progressBar.progressTintList = itemView.context.getColorStateList(android.R.color.holo_orange_dark)
                progressPercentageText.setTextColor(itemView.context.getColor(android.R.color.holo_orange_dark))
            } else {
                progressBar.progressTintList = itemView.context.getColorStateList(R.color.medium_green)
                progressPercentageText.setTextColor(itemView.context.getColor(R.color.medium_green))
            }

            cardView.setOnClickListener {
                onItemClick?.invoke(budgetProgress)
            }
        }
    }

    class BudgetProgressDiffCallback : DiffUtil.ItemCallback<BudgetProgress>() {
        override fun areItemsTheSame(oldItem: BudgetProgress, newItem: BudgetProgress): Boolean {
            return oldItem.category == newItem.category
        }

        override fun areContentsTheSame(oldItem: BudgetProgress, newItem: BudgetProgress): Boolean {
            return oldItem == newItem
        }
    }
}
