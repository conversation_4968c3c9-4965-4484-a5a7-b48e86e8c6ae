package com.arijit.budgettracker.utils

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.arijit.budgettracker.R
import com.arijit.budgettracker.db.SavingsGoal
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class SavingsGoalAdapter : ListAdapter<SavingsGoal, SavingsGoalAdapter.SavingsGoalViewHolder>(SavingsGoalDiffCallback()) {

    var onItemClick: ((SavingsGoal) -> Unit)? = null
    var onItemLongClick: ((SavingsGoal) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SavingsGoalViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_savings_goal, parent, false)
        return SavingsGoalViewHolder(view)
    }

    override fun onBindViewHolder(holder: SavingsGoalViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SavingsGoalViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleText: TextView = itemView.findViewById(R.id.goal_title_text)
        private val targetAmountText: TextView = itemView.findViewById(R.id.target_amount_text)
        private val currentAmountText: TextView = itemView.findViewById(R.id.current_amount_text)
        private val progressBar: ProgressBar = itemView.findViewById(R.id.savings_progress_bar)
        private val progressPercentageText: TextView = itemView.findViewById(R.id.savings_percentage_text)
        private val targetDateText: TextView = itemView.findViewById(R.id.target_date_text)
        private val cardView: CardView = itemView.findViewById(R.id.savings_goal_card)

        fun bind(savingsGoal: SavingsGoal) {
            val currencySymbol = CurrencyPrefs.getSymbol(itemView.context)
            val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
            
            titleText.text = savingsGoal.title
            targetAmountText.text = "$currencySymbol${savingsGoal.targetAmount.toInt()}"
            currentAmountText.text = "$currencySymbol${savingsGoal.currentAmount.toInt()}"
            
            val progressPercentage = if (savingsGoal.targetAmount > 0) {
                ((savingsGoal.currentAmount / savingsGoal.targetAmount) * 100).toInt()
            } else 0
            
            progressBar.progress = progressPercentage
            progressPercentageText.text = "$progressPercentage%"
            targetDateText.text = "Target: ${dateFormat.format(Date(savingsGoal.targetDate))}"

            // Change colors based on progress
            when {
                progressPercentage >= 100 -> {
                    progressBar.progressTintList = itemView.context.getColorStateList(R.color.medium_green)
                    progressPercentageText.setTextColor(itemView.context.getColor(R.color.medium_green))
                }
                progressPercentage >= 75 -> {
                    progressBar.progressTintList = itemView.context.getColorStateList(R.color.light_green)
                    progressPercentageText.setTextColor(itemView.context.getColor(R.color.dark_green))
                }
                else -> {
                    progressBar.progressTintList = itemView.context.getColorStateList(R.color.lime)
                    progressPercentageText.setTextColor(itemView.context.getColor(R.color.dark_green))
                }
            }

            cardView.setOnClickListener {
                onItemClick?.invoke(savingsGoal)
            }

            cardView.setOnLongClickListener {
                onItemLongClick?.invoke(savingsGoal)
                true
            }
        }
    }

    class SavingsGoalDiffCallback : DiffUtil.ItemCallback<SavingsGoal>() {
        override fun areItemsTheSame(oldItem: SavingsGoal, newItem: SavingsGoal): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: SavingsGoal, newItem: SavingsGoal): Boolean {
            return oldItem == newItem
        }
    }
}
