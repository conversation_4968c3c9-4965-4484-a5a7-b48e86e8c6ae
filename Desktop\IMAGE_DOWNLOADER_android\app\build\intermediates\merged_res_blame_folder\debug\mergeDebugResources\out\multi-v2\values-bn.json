{"logs": [{"outputFile": "com.example.imagedownloader.app-mergeDebugResources-52:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\24f3b1a12e8091b80c07f679a9055e5f\\transformed\\material3-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,193,330,449,583,700,799,915,1057,1178,1320,1405,1511,1605,1706,1835,1964,2075,2204,2331,2461,2641,2763,2883,3005,3136,3231,3326,3459,3606,3703,3808,3918,4045,4177,4284,4385,4462,4565,4665,4771,4862,4952,5055,5135,5220,5321,5425,5518,5623,5710,5816,5915,6023,6141,6221,6321", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "188,325,444,578,695,794,910,1052,1173,1315,1400,1506,1600,1701,1830,1959,2070,2199,2326,2456,2636,2758,2878,3000,3131,3226,3321,3454,3601,3698,3803,3913,4040,4172,4279,4380,4457,4560,4660,4766,4857,4947,5050,5130,5215,5316,5420,5513,5618,5705,5811,5910,6018,6136,6216,6316,6410"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4201,4339,4476,4595,4729,4846,4945,5061,5203,5324,5466,5551,5657,5751,5852,5981,6110,6221,6350,6477,6607,6787,6909,7029,7151,7282,7377,7472,7605,7752,7849,7954,8064,8191,8323,8430,8531,8608,8711,8811,8917,9008,9098,9201,9281,9366,9467,9571,9664,9769,9856,9962,10061,10169,10287,10367,10467", "endColumns": "137,136,118,133,116,98,115,141,120,141,84,105,93,100,128,128,110,128,126,129,179,121,119,121,130,94,94,132,146,96,104,109,126,131,106,100,76,102,99,105,90,89,102,79,84,100,103,92,104,86,105,98,107,117,79,99,93", "endOffsets": "4334,4471,4590,4724,4841,4940,5056,5198,5319,5461,5546,5652,5746,5847,5976,6105,6216,6345,6472,6602,6782,6904,7024,7146,7277,7372,7467,7600,7747,7844,7949,8059,8186,8318,8425,8526,8603,8706,8806,8912,9003,9093,9196,9276,9361,9462,9566,9659,9764,9851,9957,10056,10164,10282,10362,10462,10556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a843704273e916f72829d3ad6c60aff0\\transformed\\appcompat-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,10896", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,10978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\863923f39d7044ae6db3fd633409ba80\\transformed\\ui-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,283,373,471,557,636,742,829,918,996,1077,1160,1236,1313,1389,1464,1532", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,75,76,75,74,67,117", "endOffsets": "194,278,368,466,552,631,737,824,913,991,1072,1155,1231,1308,1384,1459,1527,1645"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3564,3658,3742,3832,3930,4016,4095,10561,10648,10737,10815,10983,11066,11142,11219,11396,11471,11539", "endColumns": "93,83,89,97,85,78,105,86,88,77,80,82,75,76,75,74,67,117", "endOffsets": "3653,3737,3827,3925,4011,4090,4196,10643,10732,10810,10891,11061,11137,11214,11290,11466,11534,11652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b261e901710f7c197c152ad6e2bf77a8\\transformed\\foundation-release\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11657,11742", "endColumns": "84,84", "endOffsets": "11737,11822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\273013bf131dc793dae0cb2367ffa634\\transformed\\core-1.15.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2835,2934,3036,3138,3241,3342,3444,11295", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "2929,3031,3133,3236,3337,3439,3559,11391"}}]}]}