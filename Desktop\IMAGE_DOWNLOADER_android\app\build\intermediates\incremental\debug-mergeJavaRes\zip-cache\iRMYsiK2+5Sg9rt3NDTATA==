[{"key": "androidx/compose/ui/AbsoluteAlignment.class", "name": "androidx/compose/ui/AbsoluteAlignment.class", "size": 3396, "crc": 746033721}, {"key": "androidx/compose/ui/Actual_jvmKt$tryPopulateReflectively$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/Actual_jvmKt$tryPopulateReflectively$$inlined$sortedBy$1.class", "size": 2305, "crc": -1342668316}, {"key": "androidx/compose/ui/Actual_jvmKt.class", "name": "androidx/compose/ui/Actual_jvmKt.class", "size": 3799, "crc": -1312957057}, {"key": "androidx/compose/ui/Alignment$Companion.class", "name": "androidx/compose/ui/Alignment$Companion.class", "size": 5353, "crc": -1140823115}, {"key": "androidx/compose/ui/Alignment$Horizontal.class", "name": "androidx/compose/ui/Alignment$Horizontal.class", "size": 855, "crc": -1764020708}, {"key": "androidx/compose/ui/Alignment$Vertical.class", "name": "androidx/compose/ui/Alignment$Vertical.class", "size": 629, "crc": -520774188}, {"key": "androidx/compose/ui/Alignment.class", "name": "androidx/compose/ui/Alignment.class", "size": 1271, "crc": -106797913}, {"key": "androidx/compose/ui/AtomicReference_jvmKt.class", "name": "androidx/compose/ui/AtomicReference_jvmKt.class", "size": 525, "crc": 2124077112}, {"key": "androidx/compose/ui/BiasAbsoluteAlignment$Horizontal.class", "name": "androidx/compose/ui/BiasAbsoluteAlignment$Horizontal.class", "size": 3627, "crc": -74529251}, {"key": "androidx/compose/ui/BiasAbsoluteAlignment.class", "name": "androidx/compose/ui/BiasAbsoluteAlignment.class", "size": 4447, "crc": 1856911921}, {"key": "androidx/compose/ui/BiasAlignment$Horizontal.class", "name": "androidx/compose/ui/BiasAlignment$Horizontal.class", "size": 3673, "crc": -1214206142}, {"key": "androidx/compose/ui/BiasAlignment$Vertical.class", "name": "androidx/compose/ui/BiasAlignment$Vertical.class", "size": 3365, "crc": 1675847119}, {"key": "androidx/compose/ui/BiasAlignment.class", "name": "androidx/compose/ui/BiasAlignment.class", "size": 4479, "crc": -1600300876}, {"key": "androidx/compose/ui/CombinedModifier$toString$1.class", "name": "androidx/compose/ui/CombinedModifier$toString$1.class", "size": 2015, "crc": 1664506618}, {"key": "androidx/compose/ui/CombinedModifier.class", "name": "androidx/compose/ui/CombinedModifier.class", "size": 3917, "crc": -1838439696}, {"key": "androidx/compose/ui/ComposedModifier.class", "name": "androidx/compose/ui/ComposedModifier.class", "size": 2215, "crc": 142015917}, {"key": "androidx/compose/ui/ComposedModifierKt$materializeImpl$1.class", "name": "androidx/compose/ui/ComposedModifierKt$materializeImpl$1.class", "size": 1794, "crc": 792042133}, {"key": "androidx/compose/ui/ComposedModifierKt$materializeImpl$result$1.class", "name": "androidx/compose/ui/ComposedModifierKt$materializeImpl$result$1.class", "size": 3068, "crc": -1998886002}, {"key": "androidx/compose/ui/ComposedModifierKt.class", "name": "androidx/compose/ui/ComposedModifierKt.class", "size": 10276, "crc": -1352099717}, {"key": "androidx/compose/ui/CompositionLocalMapInjectionElement.class", "name": "androidx/compose/ui/CompositionLocalMapInjectionElement.class", "size": 3190, "crc": 2069586865}, {"key": "androidx/compose/ui/CompositionLocalMapInjectionNode.class", "name": "androidx/compose/ui/CompositionLocalMapInjectionNode.class", "size": 1841, "crc": 2077933860}, {"key": "androidx/compose/ui/KeyedComposedModifier1.class", "name": "androidx/compose/ui/KeyedComposedModifier1.class", "size": 2730, "crc": 2093716977}, {"key": "androidx/compose/ui/KeyedComposedModifier2.class", "name": "androidx/compose/ui/KeyedComposedModifier2.class", "size": 3064, "crc": -1960534122}, {"key": "androidx/compose/ui/KeyedComposedModifier3.class", "name": "androidx/compose/ui/KeyedComposedModifier3.class", "size": 3362, "crc": 1603715266}, {"key": "androidx/compose/ui/KeyedComposedModifierN.class", "name": "androidx/compose/ui/KeyedComposedModifierN.class", "size": 2827, "crc": 1322939030}, {"key": "androidx/compose/ui/Modifier$Companion.class", "name": "androidx/compose/ui/Modifier$Companion.class", "size": 2535, "crc": 2104119265}, {"key": "androidx/compose/ui/Modifier$DefaultImpls.class", "name": "androidx/compose/ui/Modifier$DefaultImpls.class", "size": 825, "crc": 1618199712}, {"key": "androidx/compose/ui/Modifier$Element$DefaultImpls.class", "name": "androidx/compose/ui/Modifier$Element$DefaultImpls.class", "size": 2350, "crc": 693789658}, {"key": "androidx/compose/ui/Modifier$Element.class", "name": "androidx/compose/ui/Modifier$Element.class", "size": 3156, "crc": 321223754}, {"key": "androidx/compose/ui/Modifier$Node.class", "name": "androidx/compose/ui/Modifier$Node.class", "size": 11857, "crc": 1646220254}, {"key": "androidx/compose/ui/Modifier.class", "name": "androidx/compose/ui/Modifier.class", "size": 2547, "crc": 1248231785}, {"key": "androidx/compose/ui/ModifierNodeDetachedCancellationException.class", "name": "androidx/compose/ui/ModifierNodeDetachedCancellationException.class", "size": 1337, "crc": -1468146139}, {"key": "androidx/compose/ui/Modifier_jvmKt.class", "name": "androidx/compose/ui/Modifier_jvmKt.class", "size": 1457, "crc": -801425287}, {"key": "androidx/compose/ui/MotionDurationScale$DefaultImpls.class", "name": "androidx/compose/ui/MotionDurationScale$DefaultImpls.class", "size": 2974, "crc": 1896021740}, {"key": "androidx/compose/ui/MotionDurationScale$Key.class", "name": "androidx/compose/ui/MotionDurationScale$Key.class", "size": 1021, "crc": 283532644}, {"key": "androidx/compose/ui/MotionDurationScale.class", "name": "androidx/compose/ui/MotionDurationScale.class", "size": 1479, "crc": 1669417528}, {"key": "androidx/compose/ui/SessionMutex$Session.class", "name": "androidx/compose/ui/SessionMutex$Session.class", "size": 1359, "crc": 1791219198}, {"key": "androidx/compose/ui/SessionMutex$withSessionCancellingPrevious$2.class", "name": "androidx/compose/ui/SessionMutex$withSessionCancellingPrevious$2.class", "size": 5560, "crc": 1951272565}, {"key": "androidx/compose/ui/SessionMutex.class", "name": "androidx/compose/ui/SessionMutex.class", "size": 6650, "crc": -984530476}, {"key": "androidx/compose/ui/UiComposable.class", "name": "androidx/compose/ui/UiComposable.class", "size": 1018, "crc": -831928250}, {"key": "androidx/compose/ui/ZIndexElement.class", "name": "androidx/compose/ui/ZIndexElement.class", "size": 3786, "crc": 331891199}, {"key": "androidx/compose/ui/ZIndexModifierKt.class", "name": "androidx/compose/ui/ZIndexModifierKt.class", "size": 963, "crc": -1374053858}, {"key": "androidx/compose/ui/ZIndexNode$measure$1.class", "name": "androidx/compose/ui/ZIndexNode$measure$1.class", "size": 1961, "crc": 435546802}, {"key": "androidx/compose/ui/ZIndexNode.class", "name": "androidx/compose/ui/ZIndexNode.class", "size": 3002, "crc": 1340108499}, {"key": "androidx/compose/ui/autofill/AndroidAutofill.class", "name": "androidx/compose/ui/autofill/AndroidAutofill.class", "size": 4655, "crc": 1032656223}, {"key": "androidx/compose/ui/autofill/AndroidAutofillType_androidKt.class", "name": "androidx/compose/ui/autofill/AndroidAutofillType_androidKt.class", "size": 5197, "crc": -66659678}, {"key": "androidx/compose/ui/autofill/AndroidAutofill_androidKt.class", "name": "androidx/compose/ui/autofill/AndroidAutofill_androidKt.class", "size": 9054, "crc": 208237538}, {"key": "androidx/compose/ui/autofill/Autofill.class", "name": "androidx/compose/ui/autofill/Autofill.class", "size": 817, "crc": 755696031}, {"key": "androidx/compose/ui/autofill/AutofillApi23Helper.class", "name": "androidx/compose/ui/autofill/AutofillApi23Helper.class", "size": 2492, "crc": 1742714625}, {"key": "androidx/compose/ui/autofill/AutofillApi26Helper.class", "name": "androidx/compose/ui/autofill/AutofillApi26Helper.class", "size": 3309, "crc": -219895099}, {"key": "androidx/compose/ui/autofill/AutofillCallback.class", "name": "androidx/compose/ui/autofill/AutofillCallback.class", "size": 2871, "crc": 573562728}, {"key": "androidx/compose/ui/autofill/AutofillNode$Companion.class", "name": "androidx/compose/ui/autofill/AutofillNode$Companion.class", "size": 2188, "crc": 1675225196}, {"key": "androidx/compose/ui/autofill/AutofillNode.class", "name": "androidx/compose/ui/autofill/AutofillNode.class", "size": 4304, "crc": 790671164}, {"key": "androidx/compose/ui/autofill/AutofillTree.class", "name": "androidx/compose/ui/autofill/AutofillTree.class", "size": 2415, "crc": -805862841}, {"key": "androidx/compose/ui/autofill/AutofillType.class", "name": "androidx/compose/ui/autofill/AutofillType.class", "size": 3923, "crc": 1160501190}, {"key": "androidx/compose/ui/autofill/ContentDataType$Companion.class", "name": "androidx/compose/ui/autofill/ContentDataType$Companion.class", "size": 2382, "crc": -1551313046}, {"key": "androidx/compose/ui/autofill/ContentDataType.class", "name": "androidx/compose/ui/autofill/ContentDataType.class", "size": 3050, "crc": -1256673827}, {"key": "androidx/compose/ui/autofill/ContentType$Companion.class", "name": "androidx/compose/ui/autofill/ContentType$Companion.class", "size": 10240, "crc": -912668348}, {"key": "androidx/compose/ui/autofill/ContentType.class", "name": "androidx/compose/ui/autofill/ContentType.class", "size": 7755, "crc": -1748381341}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$TranslateStatus.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$TranslateStatus.class", "size": 1781, "crc": -1839494657}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$ViewTranslationHelperMethods.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$ViewTranslationHelperMethods.class", "size": 9639, "crc": -1929634625}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$boundsUpdatesEventLoop$1.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$boundsUpdatesEventLoop$1.class", "size": 1992, "crc": -745900069}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager.class", "size": 46275, "crc": 593256939}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureManager$Companion.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureManager$Companion.class", "size": 1266, "crc": -645931649}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureManager.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureManager.class", "size": 957, "crc": 1871021464}, {"key": "androidx/compose/ui/draganddrop/ComposeDragShadowBuilder.class", "name": "androidx/compose/ui/draganddrop/ComposeDragShadowBuilder.class", "size": 6052, "crc": -1007999499}, {"key": "androidx/compose/ui/draganddrop/DragAndDropEvent.class", "name": "androidx/compose/ui/draganddrop/DragAndDropEvent.class", "size": 1130, "crc": 40369993}, {"key": "androidx/compose/ui/draganddrop/DragAndDropManager.class", "name": "androidx/compose/ui/draganddrop/DragAndDropManager.class", "size": 1752, "crc": -589162905}, {"key": "androidx/compose/ui/draganddrop/DragAndDropModifierNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropModifierNode.class", "size": 1663, "crc": -1736570867}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion$DragAndDropTraversableKey.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion$DragAndDropTraversableKey.class", "size": 974, "crc": -1788768635}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion.class", "size": 979, "crc": 606565318}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$acceptDragAndDropTransfer$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$acceptDragAndDropTransfer$1.class", "size": 5047, "crc": -1475046721}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$onEnded$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$onEnded$1.class", "size": 2861, "crc": -204109879}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$onMoved$$inlined$firstDescendantOrNull$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$onMoved$$inlined$firstDescendantOrNull$1.class", "size": 4536, "crc": 1426243247}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode.class", "size": 9961, "crc": 889291382}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropModifierNode$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropModifierNode$1.class", "size": 1807, "crc": 35574305}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropModifierNode$2.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropModifierNode$2.class", "size": 2496, "crc": 2111586203}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$firstDescendantOrNull$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$firstDescendantOrNull$1.class", "size": 3233, "crc": 94387018}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt.class", "size": 7709, "crc": 386604930}, {"key": "androidx/compose/ui/draganddrop/DragAndDropTarget.class", "name": "androidx/compose/ui/draganddrop/DragAndDropTarget.class", "size": 1470, "crc": -298706578}, {"key": "androidx/compose/ui/draganddrop/DragAndDropTransferData.class", "name": "androidx/compose/ui/draganddrop/DragAndDropTransferData.class", "size": 1830, "crc": 575563930}, {"key": "androidx/compose/ui/draganddrop/DragAndDrop_androidKt.class", "name": "androidx/compose/ui/draganddrop/DragAndDrop_androidKt.class", "size": 2474, "crc": 1632303148}, {"key": "androidx/compose/ui/draw/AlphaKt.class", "name": "androidx/compose/ui/draw/AlphaKt.class", "size": 1141, "crc": -584555544}, {"key": "androidx/compose/ui/draw/BlurKt$blur$1.class", "name": "androidx/compose/ui/draw/BlurKt$blur$1.class", "size": 2606, "crc": 861983549}, {"key": "androidx/compose/ui/draw/BlurKt.class", "name": "androidx/compose/ui/draw/BlurKt.class", "size": 3876, "crc": -2105349511}, {"key": "androidx/compose/ui/draw/BlurredEdgeTreatment$Companion.class", "name": "androidx/compose/ui/draw/BlurredEdgeTreatment$Companion.class", "size": 1420, "crc": -1053468777}, {"key": "androidx/compose/ui/draw/BlurredEdgeTreatment.class", "name": "androidx/compose/ui/draw/BlurredEdgeTreatment.class", "size": 3594, "crc": -1135471574}, {"key": "androidx/compose/ui/draw/BuildDrawCacheParams.class", "name": "androidx/compose/ui/draw/BuildDrawCacheParams.class", "size": 963, "crc": 1236959214}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNode.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNode.class", "size": 624, "crc": 1600219600}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$1.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$1.class", "size": 1452, "crc": -2000719167}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$getOrBuildCachedDrawBlock$1$1.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$getOrBuildCachedDrawBlock$1$1.class", "size": 1665, "crc": 2090173330}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl.class", "size": 9190, "crc": 587561799}, {"key": "androidx/compose/ui/draw/CacheDrawScope$onDrawBehind$1.class", "name": "androidx/compose/ui/draw/CacheDrawScope$onDrawBehind$1.class", "size": 1933, "crc": -2019503339}, {"key": "androidx/compose/ui/draw/CacheDrawScope$record$1.class", "name": "androidx/compose/ui/draw/CacheDrawScope$record$1.class", "size": 6412, "crc": -2065277312}, {"key": "androidx/compose/ui/draw/CacheDrawScope.class", "name": "androidx/compose/ui/draw/CacheDrawScope.class", "size": 7990, "crc": 1125486010}, {"key": "androidx/compose/ui/draw/ClipKt.class", "name": "androidx/compose/ui/draw/ClipKt.class", "size": 1371, "crc": -263823444}, {"key": "androidx/compose/ui/draw/DrawBackgroundModifier.class", "name": "androidx/compose/ui/draw/DrawBackgroundModifier.class", "size": 2448, "crc": -647806366}, {"key": "androidx/compose/ui/draw/DrawBehindElement.class", "name": "androidx/compose/ui/draw/DrawBehindElement.class", "size": 4732, "crc": -1366351388}, {"key": "androidx/compose/ui/draw/DrawCacheModifier$DefaultImpls.class", "name": "androidx/compose/ui/draw/DrawCacheModifier$DefaultImpls.class", "size": 2458, "crc": 1842251480}, {"key": "androidx/compose/ui/draw/DrawCacheModifier.class", "name": "androidx/compose/ui/draw/DrawCacheModifier.class", "size": 2214, "crc": 672131218}, {"key": "androidx/compose/ui/draw/DrawModifier$DefaultImpls.class", "name": "androidx/compose/ui/draw/DrawModifier$DefaultImpls.class", "size": 2413, "crc": 1804686977}, {"key": "androidx/compose/ui/draw/DrawModifier.class", "name": "androidx/compose/ui/draw/DrawModifier.class", "size": 2133, "crc": 1755580759}, {"key": "androidx/compose/ui/draw/DrawModifierKt.class", "name": "androidx/compose/ui/draw/DrawModifierKt.class", "size": 3182, "crc": -951708506}, {"key": "androidx/compose/ui/draw/DrawResult.class", "name": "androidx/compose/ui/draw/DrawResult.class", "size": 1777, "crc": 1576035340}, {"key": "androidx/compose/ui/draw/DrawWithCacheElement.class", "name": "androidx/compose/ui/draw/DrawWithCacheElement.class", "size": 5042, "crc": 1151509562}, {"key": "androidx/compose/ui/draw/DrawWithContentElement.class", "name": "androidx/compose/ui/draw/DrawWithContentElement.class", "size": 4812, "crc": -1835096706}, {"key": "androidx/compose/ui/draw/DrawWithContentModifier.class", "name": "androidx/compose/ui/draw/DrawWithContentModifier.class", "size": 2151, "crc": -634019309}, {"key": "androidx/compose/ui/draw/EmptyBuildDrawCacheParams.class", "name": "androidx/compose/ui/draw/EmptyBuildDrawCacheParams.class", "size": 1988, "crc": -935631244}, {"key": "androidx/compose/ui/draw/PainterElement.class", "name": "androidx/compose/ui/draw/PainterElement.class", "size": 8190, "crc": -310036400}, {"key": "androidx/compose/ui/draw/PainterModifierKt.class", "name": "androidx/compose/ui/draw/PainterModifierKt.class", "size": 2714, "crc": 4386796}, {"key": "androidx/compose/ui/draw/PainterNode$measure$1.class", "name": "androidx/compose/ui/draw/PainterNode$measure$1.class", "size": 1915, "crc": -1247980185}, {"key": "androidx/compose/ui/draw/PainterNode.class", "name": "androidx/compose/ui/draw/PainterNode.class", "size": 15277, "crc": -778082239}, {"key": "androidx/compose/ui/draw/RotateKt.class", "name": "androidx/compose/ui/draw/RotateKt.class", "size": 1160, "crc": 646912312}, {"key": "androidx/compose/ui/draw/ScaleKt.class", "name": "androidx/compose/ui/draw/ScaleKt.class", "size": 1420, "crc": -185432675}, {"key": "androidx/compose/ui/draw/ScopedGraphicsContext.class", "name": "androidx/compose/ui/draw/ScopedGraphicsContext.class", "size": 4481, "crc": -1340921182}, {"key": "androidx/compose/ui/draw/ShadowGraphicsLayerElement$createBlock$1.class", "name": "androidx/compose/ui/draw/ShadowGraphicsLayerElement$createBlock$1.class", "size": 2188, "crc": 1795217313}, {"key": "androidx/compose/ui/draw/ShadowGraphicsLayerElement.class", "name": "androidx/compose/ui/draw/ShadowGraphicsLayerElement.class", "size": 7581, "crc": 2064534458}, {"key": "androidx/compose/ui/draw/ShadowKt.class", "name": "androidx/compose/ui/draw/ShadowKt.class", "size": 3989, "crc": -196670516}, {"key": "androidx/compose/ui/focus/BeyondBoundsLayoutKt.class", "name": "androidx/compose/ui/focus/BeyondBoundsLayoutKt.class", "size": 9560, "crc": 1947238758}, {"key": "androidx/compose/ui/focus/CustomDestinationResult.class", "name": "androidx/compose/ui/focus/CustomDestinationResult.class", "size": 1576, "crc": 97829963}, {"key": "androidx/compose/ui/focus/FocusChangedElement.class", "name": "androidx/compose/ui/focus/FocusChangedElement.class", "size": 4627, "crc": -1567502741}, {"key": "androidx/compose/ui/focus/FocusChangedModifierKt.class", "name": "androidx/compose/ui/focus/FocusChangedModifierKt.class", "size": 1323, "crc": -1962122638}, {"key": "androidx/compose/ui/focus/FocusChangedNode.class", "name": "androidx/compose/ui/focus/FocusChangedNode.class", "size": 2291, "crc": 1913393181}, {"key": "androidx/compose/ui/focus/FocusDirection$Companion.class", "name": "androidx/compose/ui/focus/FocusDirection$Companion.class", "size": 2439, "crc": 961856165}, {"key": "androidx/compose/ui/focus/FocusDirection.class", "name": "androidx/compose/ui/focus/FocusDirection.class", "size": 3342, "crc": 1289373551}, {"key": "androidx/compose/ui/focus/FocusEventElement.class", "name": "androidx/compose/ui/focus/FocusEventElement.class", "size": 4593, "crc": -1613093503}, {"key": "androidx/compose/ui/focus/FocusEventModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusEventModifier$DefaultImpls.class", "size": 2482, "crc": -1579434623}, {"key": "androidx/compose/ui/focus/FocusEventModifier.class", "name": "androidx/compose/ui/focus/FocusEventModifier.class", "size": 2271, "crc": 987727850}, {"key": "androidx/compose/ui/focus/FocusEventModifierKt.class", "name": "androidx/compose/ui/focus/FocusEventModifierKt.class", "size": 1313, "crc": -981553015}, {"key": "androidx/compose/ui/focus/FocusEventModifierNode.class", "name": "androidx/compose/ui/focus/FocusEventModifierNode.class", "size": 765, "crc": -958239642}, {"key": "androidx/compose/ui/focus/FocusEventModifierNodeKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusEventModifierNodeKt$WhenMappings.class", "size": 943, "crc": 1237951956}, {"key": "androidx/compose/ui/focus/FocusEventModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusEventModifierNodeKt.class", "size": 13026, "crc": -1033034597}, {"key": "androidx/compose/ui/focus/FocusEventNode.class", "name": "androidx/compose/ui/focus/FocusEventNode.class", "size": 2023, "crc": -1766043100}, {"key": "androidx/compose/ui/focus/FocusInteropUtils$Companion.class", "name": "androidx/compose/ui/focus/FocusInteropUtils$Companion.class", "size": 1114, "crc": -1380091140}, {"key": "androidx/compose/ui/focus/FocusInteropUtils.class", "name": "androidx/compose/ui/focus/FocusInteropUtils.class", "size": 1071, "crc": 545644930}, {"key": "androidx/compose/ui/focus/FocusInteropUtils_androidKt.class", "name": "androidx/compose/ui/focus/FocusInteropUtils_androidKt.class", "size": 5041, "crc": -1756312428}, {"key": "androidx/compose/ui/focus/FocusInvalidationManager$scheduleInvalidation$1.class", "name": "androidx/compose/ui/focus/FocusInvalidationManager$scheduleInvalidation$1.class", "size": 1415, "crc": -618738696}, {"key": "androidx/compose/ui/focus/FocusInvalidationManager.class", "name": "androidx/compose/ui/focus/FocusInvalidationManager.class", "size": 23372, "crc": 115292542}, {"key": "androidx/compose/ui/focus/FocusManager$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusManager$DefaultImpls.class", "size": 523, "crc": -463843009}, {"key": "androidx/compose/ui/focus/FocusManager.class", "name": "androidx/compose/ui/focus/FocusManager.class", "size": 1139, "crc": -1874763239}, {"key": "androidx/compose/ui/focus/FocusModifierKt.class", "name": "androidx/compose/ui/focus/FocusModifierKt.class", "size": 1397, "crc": 488532175}, {"key": "androidx/compose/ui/focus/FocusOrder.class", "name": "androidx/compose/ui/focus/FocusOrder.class", "size": 3606, "crc": 325361494}, {"key": "androidx/compose/ui/focus/FocusOrderModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusOrderModifier$DefaultImpls.class", "size": 2482, "crc": 474138008}, {"key": "androidx/compose/ui/focus/FocusOrderModifier.class", "name": "androidx/compose/ui/focus/FocusOrderModifier.class", "size": 2281, "crc": -1581651880}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$1.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$1.class", "size": 1750, "crc": -454274361}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$2.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$2.class", "size": 1792, "crc": 1409151117}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt.class", "size": 3316, "crc": -1311858399}, {"key": "androidx/compose/ui/focus/FocusOrderToProperties.class", "name": "androidx/compose/ui/focus/FocusOrderToProperties.class", "size": 2109, "crc": 607671721}, {"key": "androidx/compose/ui/focus/FocusOwner$dispatchKeyEvent$1.class", "name": "androidx/compose/ui/focus/FocusOwner$dispatchKeyEvent$1.class", "size": 1382, "crc": -648304136}, {"key": "androidx/compose/ui/focus/FocusOwner.class", "name": "androidx/compose/ui/focus/FocusOwner.class", "size": 4459, "crc": 1904576824}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$WhenMappings.class", "size": 948, "crc": -1953652881}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$clearFocus$clearedFocusSuccessfully$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$clearFocus$clearedFocusSuccessfully$1.class", "size": 1172, "crc": -1257036239}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$focusInvalidationManager$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$focusInvalidationManager$1.class", "size": 1515, "crc": -1808853177}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$focusSearch$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$focusSearch$1.class", "size": 2739, "crc": 1920792887}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$modifier$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$modifier$1.class", "size": 1701, "crc": -809558915}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$modifier$2.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$modifier$2.class", "size": 2891, "crc": 994397821}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$moveFocus$focusSearchSuccess$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$moveFocus$focusSearchSuccess$1.class", "size": 2218, "crc": 1147562505}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$takeFocus$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$takeFocus$1.class", "size": 1812, "crc": -1689603407}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl.class", "size": 69858, "crc": 23966679}, {"key": "androidx/compose/ui/focus/FocusOwnerImplKt.class", "name": "androidx/compose/ui/focus/FocusOwnerImplKt.class", "size": 1010, "crc": 1649031390}, {"key": "androidx/compose/ui/focus/FocusProperties$enter$1.class", "name": "androidx/compose/ui/focus/FocusProperties$enter$1.class", "size": 1835, "crc": -355205619}, {"key": "androidx/compose/ui/focus/FocusProperties$exit$1.class", "name": "androidx/compose/ui/focus/FocusProperties$exit$1.class", "size": 1832, "crc": -1880153540}, {"key": "androidx/compose/ui/focus/FocusProperties.class", "name": "androidx/compose/ui/focus/FocusProperties.class", "size": 4380, "crc": -2075240548}, {"key": "androidx/compose/ui/focus/FocusPropertiesElement.class", "name": "androidx/compose/ui/focus/FocusPropertiesElement.class", "size": 4173, "crc": -1067082364}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl$enter$1.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl$enter$1.class", "size": 1794, "crc": -976272937}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl$exit$1.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl$exit$1.class", "size": 1792, "crc": 197931707}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl.class", "size": 5390, "crc": 811476921}, {"key": "androidx/compose/ui/focus/FocusPropertiesKt$sam$androidx_compose_ui_focus_FocusPropertiesScope$0.class", "name": "androidx/compose/ui/focus/FocusPropertiesKt$sam$androidx_compose_ui_focus_FocusPropertiesScope$0.class", "size": 1837, "crc": -765427705}, {"key": "androidx/compose/ui/focus/FocusPropertiesKt.class", "name": "androidx/compose/ui/focus/FocusPropertiesKt.class", "size": 1646, "crc": -489959860}, {"key": "androidx/compose/ui/focus/FocusPropertiesModifierNode.class", "name": "androidx/compose/ui/focus/FocusPropertiesModifierNode.class", "size": 803, "crc": -1205447139}, {"key": "androidx/compose/ui/focus/FocusPropertiesModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusPropertiesModifierNodeKt.class", "size": 1146, "crc": -1240373182}, {"key": "androidx/compose/ui/focus/FocusPropertiesNode.class", "name": "androidx/compose/ui/focus/FocusPropertiesNode.class", "size": 1772, "crc": 332315622}, {"key": "androidx/compose/ui/focus/FocusPropertiesScope.class", "name": "androidx/compose/ui/focus/FocusPropertiesScope.class", "size": 677, "crc": 1345247822}, {"key": "androidx/compose/ui/focus/FocusRequester$Companion$FocusRequesterFactory.class", "name": "androidx/compose/ui/focus/FocusRequester$Companion$FocusRequesterFactory.class", "size": 2820, "crc": 447746874}, {"key": "androidx/compose/ui/focus/FocusRequester$Companion.class", "name": "androidx/compose/ui/focus/FocusRequester$Companion.class", "size": 1857, "crc": -320463172}, {"key": "androidx/compose/ui/focus/FocusRequester$focus$1.class", "name": "androidx/compose/ui/focus/FocusRequester$focus$1.class", "size": 1701, "crc": 1119801160}, {"key": "androidx/compose/ui/focus/FocusRequester.class", "name": "androidx/compose/ui/focus/FocusRequester.class", "size": 18997, "crc": -1454995128}, {"key": "androidx/compose/ui/focus/FocusRequesterElement.class", "name": "androidx/compose/ui/focus/FocusRequesterElement.class", "size": 5386, "crc": -437281174}, {"key": "androidx/compose/ui/focus/FocusRequesterKt.class", "name": "androidx/compose/ui/focus/FocusRequesterKt.class", "size": 1152, "crc": -1620116420}, {"key": "androidx/compose/ui/focus/FocusRequesterModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusRequesterModifier$DefaultImpls.class", "size": 2522, "crc": 64055513}, {"key": "androidx/compose/ui/focus/FocusRequesterModifier.class", "name": "androidx/compose/ui/focus/FocusRequesterModifier.class", "size": 2304, "crc": -1092786352}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierKt.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierKt.class", "size": 1097, "crc": -24718430}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNode.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNode.class", "size": 519, "crc": -1501641518}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt$requestFocus$1$1.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt$requestFocus$1$1.class", "size": 1808, "crc": -1360571281}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt.class", "size": 29245, "crc": 1302262345}, {"key": "androidx/compose/ui/focus/FocusRequesterNode.class", "name": "androidx/compose/ui/focus/FocusRequesterNode.class", "size": 2875, "crc": -1157328938}, {"key": "androidx/compose/ui/focus/FocusRestorerElement.class", "name": "androidx/compose/ui/focus/FocusRestorerElement.class", "size": 4630, "crc": -1202878044}, {"key": "androidx/compose/ui/focus/FocusRestorerKt$saveFocusedChild$1$1.class", "name": "androidx/compose/ui/focus/FocusRestorerKt$saveFocusedChild$1$1.class", "size": 1394, "crc": -58977011}, {"key": "androidx/compose/ui/focus/FocusRestorerKt.class", "name": "androidx/compose/ui/focus/FocusRestorerKt.class", "size": 13779, "crc": -1757399680}, {"key": "androidx/compose/ui/focus/FocusRestorerNode$onEnter$1.class", "name": "androidx/compose/ui/focus/FocusRestorerNode$onEnter$1.class", "size": 2884, "crc": 1484110624}, {"key": "androidx/compose/ui/focus/FocusRestorerNode$onExit$1.class", "name": "androidx/compose/ui/focus/FocusRestorerNode$onExit$1.class", "size": 2788, "crc": -647650092}, {"key": "androidx/compose/ui/focus/FocusRestorerNode.class", "name": "androidx/compose/ui/focus/FocusRestorerNode.class", "size": 4252, "crc": -762852816}, {"key": "androidx/compose/ui/focus/FocusState.class", "name": "androidx/compose/ui/focus/FocusState.class", "size": 554, "crc": 1251481319}, {"key": "androidx/compose/ui/focus/FocusStateImpl$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusStateImpl$WhenMappings.class", "size": 865, "crc": 925860260}, {"key": "androidx/compose/ui/focus/FocusStateImpl.class", "name": "androidx/compose/ui/focus/FocusStateImpl.class", "size": 2459, "crc": 1560299100}, {"key": "androidx/compose/ui/focus/FocusTargetModifierNode.class", "name": "androidx/compose/ui/focus/FocusTargetModifierNode.class", "size": 976, "crc": 1425372126}, {"key": "androidx/compose/ui/focus/FocusTargetModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusTargetModifierNodeKt.class", "size": 767, "crc": -1546296067}, {"key": "androidx/compose/ui/focus/FocusTargetNode$FocusTargetElement.class", "name": "androidx/compose/ui/focus/FocusTargetNode$FocusTargetElement.class", "size": 2703, "crc": 1885119308}, {"key": "androidx/compose/ui/focus/FocusTargetNode$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTargetNode$WhenMappings.class", "size": 918, "crc": 1097865771}, {"key": "androidx/compose/ui/focus/FocusTargetNode$invalidateFocus$1.class", "name": "androidx/compose/ui/focus/FocusTargetNode$invalidateFocus$1.class", "size": 1799, "crc": 1765624923}, {"key": "androidx/compose/ui/focus/FocusTargetNode.class", "name": "androidx/compose/ui/focus/FocusTargetNode.class", "size": 25889, "crc": -203107642}, {"key": "androidx/compose/ui/focus/FocusTargetNodeKt.class", "name": "androidx/compose/ui/focus/FocusTargetNodeKt.class", "size": 2493, "crc": 1159699091}, {"key": "androidx/compose/ui/focus/FocusTransactionManager.class", "name": "androidx/compose/ui/focus/FocusTransactionManager.class", "size": 10506, "crc": 1360774836}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt$WhenMappings.class", "size": 1361, "crc": -610087776}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt$grantFocus$1.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt$grantFocus$1.class", "size": 1401, "crc": -872771552}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt$requestFocus$1.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt$requestFocus$1.class", "size": 1649, "crc": -177716499}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt.class", "size": 28104, "crc": -1991182560}, {"key": "androidx/compose/ui/focus/FocusTraversalKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTraversalKt$WhenMappings.class", "size": 1211, "crc": 547569525}, {"key": "androidx/compose/ui/focus/FocusTraversalKt.class", "name": "androidx/compose/ui/focus/FocusTraversalKt.class", "size": 20384, "crc": -103165900}, {"key": "androidx/compose/ui/focus/FocusableChildrenComparator.class", "name": "androidx/compose/ui/focus/FocusableChildrenComparator.class", "size": 5153, "crc": 427654017}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings.class", "size": 952, "crc": 799541636}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "size": 2972, "crc": 1012088201}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt.class", "size": 28245, "crc": 771226951}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$WhenMappings.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$WhenMappings.class", "size": 952, "crc": -725334427}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "size": 2986, "crc": -411220744}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt.class", "size": 24056, "crc": -1584171769}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerElement.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerElement.class", "size": 4901, "crc": 188872843}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier$measure$1.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier$measure$1.class", "size": 2252, "crc": -672451117}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier.class", "size": 5437, "crc": -1826563815}, {"key": "androidx/compose/ui/graphics/CompositingStrategy$Companion.class", "name": "androidx/compose/ui/graphics/CompositingStrategy$Companion.class", "size": 1451, "crc": -278711472}, {"key": "androidx/compose/ui/graphics/CompositingStrategy.class", "name": "androidx/compose/ui/graphics/CompositingStrategy.class", "size": 2871, "crc": 60077760}, {"key": "androidx/compose/ui/graphics/Fields.class", "name": "androidx/compose/ui/graphics/Fields.class", "size": 1845, "crc": -1333828428}, {"key": "androidx/compose/ui/graphics/GraphicsContextObserver.class", "name": "androidx/compose/ui/graphics/GraphicsContextObserver.class", "size": 1753, "crc": 117261633}, {"key": "androidx/compose/ui/graphics/GraphicsLayerElement.class", "name": "androidx/compose/ui/graphics/GraphicsLayerElement.class", "size": 13322, "crc": -721791656}, {"key": "androidx/compose/ui/graphics/GraphicsLayerModifierKt.class", "name": "androidx/compose/ui/graphics/GraphicsLayerModifierKt.class", "size": 9913, "crc": 1276411011}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScope$DefaultImpls.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScope$DefaultImpls.class", "size": 5595, "crc": 1469232869}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScope.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScope.class", "size": 8274, "crc": 1722849078}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScopeKt.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScopeKt.class", "size": 5054, "crc": -461406864}, {"key": "androidx/compose/ui/graphics/ReusableGraphicsLayerScope.class", "name": "androidx/compose/ui/graphics/ReusableGraphicsLayerScope.class", "size": 11331, "crc": 64012458}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$layerBlock$1.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$layerBlock$1.class", "size": 3226, "crc": -465510968}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$measure$1.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$measure$1.class", "size": 2324, "crc": -1900374710}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier.class", "size": 12560, "crc": -175314650}, {"key": "androidx/compose/ui/graphics/TransformOrigin$Companion.class", "name": "androidx/compose/ui/graphics/TransformOrigin$Companion.class", "size": 1089, "crc": -1153217947}, {"key": "androidx/compose/ui/graphics/TransformOrigin.class", "name": "androidx/compose/ui/graphics/TransformOrigin.class", "size": 5133, "crc": -1837094189}, {"key": "androidx/compose/ui/graphics/TransformOriginKt.class", "name": "androidx/compose/ui/graphics/TransformOriginKt.class", "size": 1641, "crc": -1054813224}, {"key": "androidx/compose/ui/graphics/vector/DrawCache.class", "name": "androidx/compose/ui/graphics/vector/DrawCache.class", "size": 9261, "crc": -153578011}, {"key": "androidx/compose/ui/graphics/vector/GroupComponent$wrappedListener$1.class", "name": "androidx/compose/ui/graphics/vector/GroupComponent$wrappedListener$1.class", "size": 1881, "crc": -2127925341}, {"key": "androidx/compose/ui/graphics/vector/GroupComponent.class", "name": "androidx/compose/ui/graphics/vector/GroupComponent.class", "size": 15450, "crc": -298222800}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Builder$GroupParams.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Builder$GroupParams.class", "size": 5291, "crc": 82749411}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Builder.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Builder.class", "size": 12075, "crc": -1452398358}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Companion.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Companion.class", "size": 2166, "crc": 527959855}, {"key": "androidx/compose/ui/graphics/vector/ImageVector.class", "name": "androidx/compose/ui/graphics/vector/ImageVector.class", "size": 5528, "crc": -1396446020}, {"key": "androidx/compose/ui/graphics/vector/ImageVectorKt.class", "name": "androidx/compose/ui/graphics/vector/ImageVectorKt.class", "size": 8442, "crc": -93165167}, {"key": "androidx/compose/ui/graphics/vector/PathComponent$pathMeasure$2.class", "name": "androidx/compose/ui/graphics/vector/PathComponent$pathMeasure$2.class", "size": 1349, "crc": -921816343}, {"key": "androidx/compose/ui/graphics/vector/PathComponent.class", "name": "androidx/compose/ui/graphics/vector/PathComponent.class", "size": 10563, "crc": 1644020949}, {"key": "androidx/compose/ui/graphics/vector/VNode.class", "name": "androidx/compose/ui/graphics/vector/VNode.class", "size": 2433, "crc": 3754666}, {"key": "androidx/compose/ui/graphics/vector/VectorApplier.class", "name": "androidx/compose/ui/graphics/vector/VectorApplier.class", "size": 3514, "crc": 821989908}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$1.class", "size": 1651, "crc": 714632983}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$drawVectorBlock$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$drawVectorBlock$1.class", "size": 4812, "crc": 1689213763}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$invalidateCallback$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$invalidateCallback$1.class", "size": 1190, "crc": 1446020400}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent.class", "size": 11336, "crc": -404098600}, {"key": "androidx/compose/ui/graphics/vector/VectorComposable.class", "name": "androidx/compose/ui/graphics/vector/VectorComposable.class", "size": 1066, "crc": -130420113}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$1.class", "size": 1487, "crc": 346511151}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$1.class", "size": 1850, "crc": -98123456}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$2.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$2.class", "size": 1830, "crc": -2133581940}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$3.class", "size": 1828, "crc": -599251202}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$4.class", "size": 1828, "crc": -1709042604}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$5.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$5.class", "size": 1828, "crc": 547179934}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$6.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$6.class", "size": 1828, "crc": 1764680694}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$7.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$7.class", "size": 1834, "crc": 1936627077}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$8.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$8.class", "size": 1834, "crc": -262500484}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$9.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$9.class", "size": 2090, "crc": -1380655532}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$4.class", "size": 2746, "crc": -704690975}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$1.class", "size": 1532, "crc": 539102818}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$1.class", "size": 1895, "crc": 395268956}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$10.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$10.class", "size": 1994, "crc": -745561894}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$11.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$11.class", "size": 1884, "crc": 72402529}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$12.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$12.class", "size": 1882, "crc": 214465993}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$13.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$13.class", "size": 1880, "crc": -316052676}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$14.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$14.class", "size": 1883, "crc": -312621593}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$2.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$2.class", "size": 2130, "crc": 683448590}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$3.class", "size": 2000, "crc": 683658078}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$4.class", "size": 2026, "crc": -1695876820}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$5.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$5.class", "size": 1876, "crc": -960324546}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$6.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$6.class", "size": 2028, "crc": 609716576}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$7.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$7.class", "size": 1878, "crc": -1739678400}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$8.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$8.class", "size": 1882, "crc": -348653280}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$9.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$9.class", "size": 1996, "crc": -389091306}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$3.class", "size": 2996, "crc": -266316133}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt.class", "size": 15359, "crc": -219700625}, {"key": "androidx/compose/ui/graphics/vector/VectorConfig$DefaultImpls.class", "name": "androidx/compose/ui/graphics/vector/VectorConfig$DefaultImpls.class", "size": 1179, "crc": 1948114034}, {"key": "androidx/compose/ui/graphics/vector/VectorConfig.class", "name": "androidx/compose/ui/graphics/vector/VectorConfig.class", "size": 1410, "crc": -55985423}, {"key": "androidx/compose/ui/graphics/vector/VectorGroup$iterator$1.class", "name": "androidx/compose/ui/graphics/vector/VectorGroup$iterator$1.class", "size": 2310, "crc": -1562927552}, {"key": "androidx/compose/ui/graphics/vector/VectorGroup.class", "name": "androidx/compose/ui/graphics/vector/VectorGroup.class", "size": 6086, "crc": 451644414}, {"key": "androidx/compose/ui/graphics/vector/VectorKt.class", "name": "androidx/compose/ui/graphics/vector/VectorKt.class", "size": 6327, "crc": -2144632170}, {"key": "androidx/compose/ui/graphics/vector/VectorNode.class", "name": "androidx/compose/ui/graphics/vector/VectorNode.class", "size": 1025, "crc": 564054590}, {"key": "androidx/compose/ui/graphics/vector/VectorPainter$vector$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainter$vector$1$1.class", "size": 1552, "crc": -1928167839}, {"key": "androidx/compose/ui/graphics/vector/VectorPainter.class", "name": "androidx/compose/ui/graphics/vector/VectorPainter.class", "size": 12295, "crc": 1970180320}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$1.class", "size": 2931, "crc": 1328409513}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$2.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$2.class", "size": 2203, "crc": 1981936604}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$1.class", "size": 945, "crc": -1629001938}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$2.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$2.class", "size": 945, "crc": 233413349}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1.class", "size": 3225, "crc": 315232172}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt.class", "size": 30674, "crc": -1861673853}, {"key": "androidx/compose/ui/graphics/vector/VectorPath.class", "name": "androidx/compose/ui/graphics/vector/VectorPath.class", "size": 7070, "crc": 1391745897}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Fill.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Fill.class", "size": 1177, "crc": -972381787}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$FillAlpha.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$FillAlpha.class", "size": 1135, "crc": 110420774}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PathData.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PathData.class", "size": 1242, "crc": -1386074234}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PivotX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PivotX.class", "size": 1126, "crc": 1441560700}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PivotY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PivotY.class", "size": 1126, "crc": -1934482490}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Rotation.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Rotation.class", "size": 1132, "crc": -26176918}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleX.class", "size": 1126, "crc": 2021279312}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleY.class", "size": 1126, "crc": -290331537}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Stroke.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Stroke.class", "size": 1183, "crc": -453936561}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeAlpha.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeAlpha.class", "size": 1141, "crc": 262604497}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeLineWidth.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeLineWidth.class", "size": 1153, "crc": 1289896990}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateX.class", "size": 1138, "crc": -343333642}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateY.class", "size": 1138, "crc": 1522296527}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathEnd.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathEnd.class", "size": 1141, "crc": -298042659}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathOffset.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathOffset.class", "size": 1150, "crc": -170466607}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathStart.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathStart.class", "size": 1147, "crc": -892021628}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty.class", "size": 3632, "crc": -635478668}, {"key": "androidx/compose/ui/graphics/vector/compat/AndroidVectorParser.class", "name": "androidx/compose/ui/graphics/vector/compat/AndroidVectorParser.class", "size": 9320, "crc": -1982477683}, {"key": "androidx/compose/ui/graphics/vector/compat/AndroidVectorResources.class", "name": "androidx/compose/ui/graphics/vector/compat/AndroidVectorResources.class", "size": 9164, "crc": -498134575}, {"key": "androidx/compose/ui/graphics/vector/compat/XmlVectorParser_androidKt.class", "name": "androidx/compose/ui/graphics/vector/compat/XmlVectorParser_androidKt.class", "size": 18886, "crc": 791482408}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedback.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedback.class", "size": 633, "crc": 858813403}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedbackType$Companion.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedbackType$Companion.class", "size": 1923, "crc": 86156760}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedbackType.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedbackType.class", "size": 2524, "crc": 76048886}, {"key": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedback.class", "name": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedback.class", "size": 1890, "crc": -1793229673}, {"key": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedbackType.class", "name": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedbackType.class", "size": 1399, "crc": 1055854013}, {"key": "androidx/compose/ui/input/InputMode$Companion.class", "name": "androidx/compose/ui/input/InputMode$Companion.class", "size": 1215, "crc": -883034790}, {"key": "androidx/compose/ui/input/InputMode.class", "name": "androidx/compose/ui/input/InputMode.class", "size": 2551, "crc": -1778239118}, {"key": "androidx/compose/ui/input/InputModeManager.class", "name": "androidx/compose/ui/input/InputModeManager.class", "size": 754, "crc": 1093596814}, {"key": "androidx/compose/ui/input/InputModeManagerImpl.class", "name": "androidx/compose/ui/input/InputModeManagerImpl.class", "size": 4059, "crc": -480186770}, {"key": "androidx/compose/ui/input/key/InterceptedKeyInputNode.class", "name": "androidx/compose/ui/input/key/InterceptedKeyInputNode.class", "size": 3373, "crc": -1495931052}, {"key": "androidx/compose/ui/input/key/Key$Companion.class", "name": "androidx/compose/ui/input/key/Key$Companion.class", "size": 49321, "crc": -1998875440}, {"key": "androidx/compose/ui/input/key/Key.class", "name": "androidx/compose/ui/input/key/Key.class", "size": 33732, "crc": 376237039}, {"key": "androidx/compose/ui/input/key/KeyEvent.class", "name": "androidx/compose/ui/input/key/KeyEvent.class", "size": 2729, "crc": 130771969}, {"key": "androidx/compose/ui/input/key/KeyEventType$Companion.class", "name": "androidx/compose/ui/input/key/KeyEventType$Companion.class", "size": 1393, "crc": 1204194060}, {"key": "androidx/compose/ui/input/key/KeyEventType.class", "name": "androidx/compose/ui/input/key/KeyEventType.class", "size": 2705, "crc": 2125835828}, {"key": "androidx/compose/ui/input/key/KeyEvent_androidKt.class", "name": "androidx/compose/ui/input/key/KeyEvent_androidKt.class", "size": 2531, "crc": -348709504}, {"key": "androidx/compose/ui/input/key/KeyInputElement.class", "name": "androidx/compose/ui/input/key/KeyInputElement.class", "size": 5873, "crc": 2141101638}, {"key": "androidx/compose/ui/input/key/KeyInputModifierKt.class", "name": "androidx/compose/ui/input/key/KeyInputModifierKt.class", "size": 1560, "crc": 806443323}, {"key": "androidx/compose/ui/input/key/KeyInputModifierNode.class", "name": "androidx/compose/ui/input/key/KeyInputModifierNode.class", "size": 893, "crc": 1185279867}, {"key": "androidx/compose/ui/input/key/KeyInputNode.class", "name": "androidx/compose/ui/input/key/KeyInputNode.class", "size": 3211, "crc": 405031341}, {"key": "androidx/compose/ui/input/key/Key_androidKt.class", "name": "androidx/compose/ui/input/key/Key_androidKt.class", "size": 1682, "crc": -487338180}, {"key": "androidx/compose/ui/input/key/SoftKeyboardInterceptionElement.class", "name": "androidx/compose/ui/input/key/SoftKeyboardInterceptionElement.class", "size": 6167, "crc": 1013788411}, {"key": "androidx/compose/ui/input/key/SoftKeyboardInterceptionModifierNode.class", "name": "androidx/compose/ui/input/key/SoftKeyboardInterceptionModifierNode.class", "size": 1120, "crc": -931642711}, {"key": "androidx/compose/ui/input/key/SoftwareKeyboardInterceptionModifierKt.class", "name": "androidx/compose/ui/input/key/SoftwareKeyboardInterceptionModifierKt.class", "size": 1789, "crc": -598713716}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection$DefaultImpls.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection$DefaultImpls.class", "size": 2319, "crc": 1911105186}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection.class", "size": 4081, "crc": -1957814388}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$calculateNestedScrollScope$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$calculateNestedScrollScope$1.class", "size": 1467, "crc": -367293367}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPostFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPostFling$1.class", "size": 1887, "crc": -247241029}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPreFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPreFling$1.class", "size": 1881, "crc": -1690692947}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher.class", "size": 6989, "crc": 1848843079}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollElement.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollElement.class", "size": 3976, "crc": 566208166}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollModifierKt.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollModifierKt.class", "size": 1809, "crc": -968971276}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPostFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPostFling$1.class", "size": 1939, "crc": -994668459}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPreFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPreFling$1.class", "size": 1904, "crc": 358164603}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$updateDispatcherFields$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$updateDispatcherFields$1.class", "size": 1555, "crc": 894431018}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode.class", "size": 9421, "crc": -69991259}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt.class", "size": 1356, "crc": 1310615644}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollSource$Companion.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollSource$Companion.class", "size": 2991, "crc": -1449345367}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollSource.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollSource.class", "size": 3152, "crc": 1905819902}, {"key": "androidx/compose/ui/input/pointer/AndroidPointerIcon.class", "name": "androidx/compose/ui/input/pointer/AndroidPointerIcon.class", "size": 2445, "crc": -605943285}, {"key": "androidx/compose/ui/input/pointer/AndroidPointerIconType.class", "name": "androidx/compose/ui/input/pointer/AndroidPointerIconType.class", "size": 2253, "crc": 729488696}, {"key": "androidx/compose/ui/input/pointer/AwaitPointerEventScope$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/AwaitPointerEventScope$DefaultImpls.class", "size": 5126, "crc": -1572365486}, {"key": "androidx/compose/ui/input/pointer/AwaitPointerEventScope.class", "name": "androidx/compose/ui/input/pointer/AwaitPointerEventScope.class", "size": 7518, "crc": 1847680140}, {"key": "androidx/compose/ui/input/pointer/CancelTimeoutCancellationException.class", "name": "androidx/compose/ui/input/pointer/CancelTimeoutCancellationException.class", "size": 1398, "crc": 912355563}, {"key": "androidx/compose/ui/input/pointer/ConsumedData.class", "name": "androidx/compose/ui/input/pointer/ConsumedData.class", "size": 2023, "crc": -1041239303}, {"key": "androidx/compose/ui/input/pointer/HistoricalChange.class", "name": "androidx/compose/ui/input/pointer/HistoricalChange.class", "size": 2740, "crc": 32631528}, {"key": "androidx/compose/ui/input/pointer/HitPathTracker.class", "name": "androidx/compose/ui/input/pointer/HitPathTracker.class", "size": 10638, "crc": 1486548564}, {"key": "androidx/compose/ui/input/pointer/InternalPointerEvent.class", "name": "androidx/compose/ui/input/pointer/InternalPointerEvent.class", "size": 4839, "crc": -431058803}, {"key": "androidx/compose/ui/input/pointer/MotionEventAdapter.class", "name": "androidx/compose/ui/input/pointer/MotionEventAdapter.class", "size": 9562, "crc": -1143931254}, {"key": "androidx/compose/ui/input/pointer/MotionEventHelper.class", "name": "androidx/compose/ui/input/pointer/MotionEventHelper.class", "size": 1400, "crc": -670690154}, {"key": "androidx/compose/ui/input/pointer/Node.class", "name": "androidx/compose/ui/input/pointer/Node.class", "size": 25415, "crc": 400264844}, {"key": "androidx/compose/ui/input/pointer/NodeParent.class", "name": "androidx/compose/ui/input/pointer/NodeParent.class", "size": 8103, "crc": -1816025759}, {"key": "androidx/compose/ui/input/pointer/PointerButtons.class", "name": "androidx/compose/ui/input/pointer/PointerButtons.class", "size": 2166, "crc": 1006770743}, {"key": "androidx/compose/ui/input/pointer/PointerEvent.class", "name": "androidx/compose/ui/input/pointer/PointerEvent.class", "size": 8511, "crc": 2070490514}, {"key": "androidx/compose/ui/input/pointer/PointerEventKt.class", "name": "androidx/compose/ui/input/pointer/PointerEventKt.class", "size": 6344, "crc": 1437835784}, {"key": "androidx/compose/ui/input/pointer/PointerEventPass.class", "name": "androidx/compose/ui/input/pointer/PointerEventPass.class", "size": 1503, "crc": 2146609944}, {"key": "androidx/compose/ui/input/pointer/PointerEventTimeoutCancellationException.class", "name": "androidx/compose/ui/input/pointer/PointerEventTimeoutCancellationException.class", "size": 1679, "crc": -1842580301}, {"key": "androidx/compose/ui/input/pointer/PointerEventType$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerEventType$Companion.class", "size": 2046, "crc": 879010526}, {"key": "androidx/compose/ui/input/pointer/PointerEventType.class", "name": "androidx/compose/ui/input/pointer/PointerEventType.class", "size": 3206, "crc": -1535981392}, {"key": "androidx/compose/ui/input/pointer/PointerEvent_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerEvent_androidKt.class", "size": 4590, "crc": 308050663}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierElement.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierElement.class", "size": 5307, "crc": -281197570}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode$displayIconFromAncestorNodeWithCursorInBoundsOrDefaultIcon$1.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode$displayIconFromAncestorNodeWithCursorInBoundsOrDefaultIcon$1.class", "size": 2452, "crc": -218270343}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode$displayIconIfDescendantsDoNotHavePriority$1.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode$displayIconIfDescendantsDoNotHavePriority$1.class", "size": 2552, "crc": 1442481108}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode$findDescendantNodeWithCursorInBounds$1.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode$findDescendantNodeWithCursorInBounds$1.class", "size": 2902, "crc": -1056789047}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode$findOverridingAncestorNode$1.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode$findOverridingAncestorNode$1.class", "size": 2368, "crc": 243457594}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode.class", "size": 8529, "crc": 983253910}, {"key": "androidx/compose/ui/input/pointer/PointerIcon$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerIcon$Companion.class", "size": 1760, "crc": 229208088}, {"key": "androidx/compose/ui/input/pointer/PointerIcon.class", "name": "androidx/compose/ui/input/pointer/PointerIcon.class", "size": 800, "crc": -257735197}, {"key": "androidx/compose/ui/input/pointer/PointerIconKt.class", "name": "androidx/compose/ui/input/pointer/PointerIconKt.class", "size": 1504, "crc": -1696354699}, {"key": "androidx/compose/ui/input/pointer/PointerIconService.class", "name": "androidx/compose/ui/input/pointer/PointerIconService.class", "size": 845, "crc": -248156959}, {"key": "androidx/compose/ui/input/pointer/PointerIcon_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerIcon_androidKt.class", "size": 2048, "crc": -907969439}, {"key": "androidx/compose/ui/input/pointer/PointerId.class", "name": "androidx/compose/ui/input/pointer/PointerId.class", "size": 2156, "crc": -1358844798}, {"key": "androidx/compose/ui/input/pointer/PointerInputChange.class", "name": "androidx/compose/ui/input/pointer/PointerInputChange.class", "size": 17838, "crc": 760165650}, {"key": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer$PointerInputData.class", "name": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer$PointerInputData.class", "size": 1856, "crc": -469578680}, {"key": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer.class", "name": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer.class", "size": 5058, "crc": -341535200}, {"key": "androidx/compose/ui/input/pointer/PointerInputEvent.class", "name": "androidx/compose/ui/input/pointer/PointerInputEvent.class", "size": 1957, "crc": -924913842}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventData.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventData.class", "size": 8572, "crc": 1823236131}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventProcessor.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventProcessor.class", "size": 6318, "crc": -788002055}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventProcessorKt.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventProcessorKt.class", "size": 857, "crc": 1318620097}, {"key": "androidx/compose/ui/input/pointer/PointerInputFilter.class", "name": "androidx/compose/ui/input/pointer/PointerInputFilter.class", "size": 2980, "crc": -336236284}, {"key": "androidx/compose/ui/input/pointer/PointerInputModifier$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/PointerInputModifier$DefaultImpls.class", "size": 2566, "crc": -298308274}, {"key": "androidx/compose/ui/input/pointer/PointerInputModifier.class", "name": "androidx/compose/ui/input/pointer/PointerInputModifier.class", "size": 2261, "crc": 1137420460}, {"key": "androidx/compose/ui/input/pointer/PointerInputResetException.class", "name": "androidx/compose/ui/input/pointer/PointerInputResetException.class", "size": 1382, "crc": 1237843010}, {"key": "androidx/compose/ui/input/pointer/PointerInputScope$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/PointerInputScope$DefaultImpls.class", "size": 4346, "crc": 1621287738}, {"key": "androidx/compose/ui/input/pointer/PointerInputScope.class", "name": "androidx/compose/ui/input/pointer/PointerInputScope.class", "size": 5527, "crc": 1275684960}, {"key": "androidx/compose/ui/input/pointer/PointerInputTestUtilKt.class", "name": "androidx/compose/ui/input/pointer/PointerInputTestUtilKt.class", "size": 10750, "crc": 152058287}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$DispatchToViewState.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$DispatchToViewState.class", "size": 1800, "crc": 890677361}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$2.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$2.class", "size": 1853, "crc": -697384662}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$3.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$3.class", "size": 2686, "crc": 866874761}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$onCancel$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$onCancel$1.class", "size": 1787, "crc": -1635537779}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1.class", "size": 9136, "crc": -264934386}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter.class", "size": 3785, "crc": 510155869}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1$1.class", "size": 4540, "crc": 1049011483}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1.class", "size": 4142, "crc": -998591057}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$$inlined$debugInspectorInfo$1.class", "size": 3452, "crc": 1835705953}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$2.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$2.class", "size": 5495, "crc": 1583823725}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$3.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$3.class", "size": 2100, "crc": 2036391443}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt.class", "size": 5962, "crc": 1122231886}, {"key": "androidx/compose/ui/input/pointer/PointerInteropUtils_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerInteropUtils_androidKt.class", "size": 3841, "crc": -914547308}, {"key": "androidx/compose/ui/input/pointer/PointerKeyboardModifiers.class", "name": "androidx/compose/ui/input/pointer/PointerKeyboardModifiers.class", "size": 2216, "crc": -41838727}, {"key": "androidx/compose/ui/input/pointer/PointerType$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerType$Companion.class", "size": 1719, "crc": -542985654}, {"key": "androidx/compose/ui/input/pointer/PointerType.class", "name": "androidx/compose/ui/input/pointer/PointerType.class", "size": 2914, "crc": 713900699}, {"key": "androidx/compose/ui/input/pointer/PositionCalculator.class", "name": "androidx/compose/ui/input/pointer/PositionCalculator.class", "size": 957, "crc": 926273163}, {"key": "androidx/compose/ui/input/pointer/ProcessResult.class", "name": "androidx/compose/ui/input/pointer/ProcessResult.class", "size": 2438, "crc": 203930617}, {"key": "androidx/compose/ui/input/pointer/RequestDisallowInterceptTouchEvent.class", "name": "androidx/compose/ui/input/pointer/RequestDisallowInterceptTouchEvent.class", "size": 2201, "crc": -1046067951}, {"key": "androidx/compose/ui/input/pointer/SuspendPointerInputElement.class", "name": "androidx/compose/ui/input/pointer/SuspendPointerInputElement.class", "size": 5800, "crc": -897748885}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt.class", "size": 5745, "crc": -1474509486}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilter_jvmKt.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilter_jvmKt.class", "size": 1767, "crc": 39361147}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNode.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNode.class", "size": 1632, "crc": 1451320579}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$1.class", "size": 2566, "crc": 1305919225}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$job$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$job$1.class", "size": 5095, "crc": 869992093}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeoutOrNull$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeoutOrNull$1.class", "size": 2561, "crc": 503439272}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine.class", "size": 16311, "crc": 519573236}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$WhenMappings.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$WhenMappings.class", "size": 965, "crc": -486708125}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$awaitPointerEventScope$2$2.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$awaitPointerEventScope$2$2.class", "size": 2240, "crc": -801976163}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$onPointerEvent$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$onPointerEvent$1.class", "size": 3834, "crc": -2044234530}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl.class", "size": 23631, "crc": -1028270667}, {"key": "androidx/compose/ui/input/pointer/util/DataPointAtTime.class", "name": "androidx/compose/ui/input/pointer/util/DataPointAtTime.class", "size": 2950, "crc": 997812678}, {"key": "androidx/compose/ui/input/pointer/util/PointerIdArray.class", "name": "androidx/compose/ui/input/pointer/util/PointerIdArray.class", "size": 3552, "crc": -1692072269}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker.class", "size": 5271, "crc": -361540514}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$Strategy.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$Strategy.class", "size": 1649, "crc": -1777126126}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$WhenMappings.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$WhenMappings.class", "size": 939, "crc": -1405681881}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D.class", "size": 6673, "crc": 175900334}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTrackerKt.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTrackerKt.class", "size": 12081, "crc": 451715590}, {"key": "androidx/compose/ui/input/rotary/RotaryInputElement.class", "name": "androidx/compose/ui/input/rotary/RotaryInputElement.class", "size": 6085, "crc": -298940669}, {"key": "androidx/compose/ui/input/rotary/RotaryInputModifierKt.class", "name": "androidx/compose/ui/input/rotary/RotaryInputModifierKt.class", "size": 1659, "crc": 1706428310}, {"key": "androidx/compose/ui/input/rotary/RotaryInputModifierNode.class", "name": "androidx/compose/ui/input/rotary/RotaryInputModifierNode.class", "size": 880, "crc": -252424293}, {"key": "androidx/compose/ui/input/rotary/RotaryInputNode.class", "name": "androidx/compose/ui/input/rotary/RotaryInputNode.class", "size": 3100, "crc": -1464006280}, {"key": "androidx/compose/ui/input/rotary/RotaryScrollEvent.class", "name": "androidx/compose/ui/input/rotary/RotaryScrollEvent.class", "size": 3655, "crc": -1458636316}, {"key": "androidx/compose/ui/internal/InlineClassHelperKt.class", "name": "androidx/compose/ui/internal/InlineClassHelperKt.class", "size": 3029, "crc": 168355090}, {"key": "androidx/compose/ui/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/ui/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 550, "crc": -430103761}, {"key": "androidx/compose/ui/layout/AlignmentLine$Companion.class", "name": "androidx/compose/ui/layout/AlignmentLine$Companion.class", "size": 871, "crc": -1050363995}, {"key": "androidx/compose/ui/layout/AlignmentLine.class", "name": "androidx/compose/ui/layout/AlignmentLine.class", "size": 2097, "crc": -1209077545}, {"key": "androidx/compose/ui/layout/AlignmentLineKt$FirstBaseline$1.class", "name": "androidx/compose/ui/layout/AlignmentLineKt$FirstBaseline$1.class", "size": 1556, "crc": -677696486}, {"key": "androidx/compose/ui/layout/AlignmentLineKt$LastBaseline$1.class", "name": "androidx/compose/ui/layout/AlignmentLineKt$LastBaseline$1.class", "size": 1554, "crc": -1409360866}, {"key": "androidx/compose/ui/layout/AlignmentLineKt.class", "name": "androidx/compose/ui/layout/AlignmentLineKt.class", "size": 2076, "crc": 1118361206}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicMeasureScope.class", "size": 983, "crc": 45798464}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope$layout$1.class", "size": 2750, "crc": -51222201}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope.class", "size": 8371, "crc": -339342050}, {"key": "androidx/compose/ui/layout/ApproachLayoutElement.class", "name": "androidx/compose/ui/layout/ApproachLayoutElement.class", "size": 9113, "crc": 1477990155}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicHeight$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicHeight$1.class", "size": 1938, "crc": -771567447}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicWidth$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicWidth$1.class", "size": 1935, "crc": -137055046}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$measure$1$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$measure$1$1.class", "size": 1984, "crc": -1299526180}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicHeight$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicHeight$1.class", "size": 1938, "crc": 1965192177}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicWidth$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicWidth$1.class", "size": 1935, "crc": 289696870}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode.class", "size": 5480, "crc": -1900851300}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNodeImpl.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNodeImpl.class", "size": 6737, "crc": -2094960948}, {"key": "androidx/compose/ui/layout/ApproachMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachMeasureScope.class", "size": 775, "crc": 1812168817}, {"key": "androidx/compose/ui/layout/ApproachMeasureScopeImpl$layout$1.class", "name": "androidx/compose/ui/layout/ApproachMeasureScopeImpl$layout$1.class", "size": 3886, "crc": -116138107}, {"key": "androidx/compose/ui/layout/ApproachMeasureScopeImpl.class", "name": "androidx/compose/ui/layout/ApproachMeasureScopeImpl.class", "size": 13095, "crc": -1296811059}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$BeyondBoundsScope.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$BeyondBoundsScope.class", "size": 639, "crc": 2039389055}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection$Companion.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection$Companion.class", "size": 2009, "crc": -1122276351}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection.class", "size": 3265, "crc": -1605496409}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout.class", "size": 1390, "crc": 1189315242}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayoutKt$ModifierLocalBeyondBoundsLayout$1.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayoutKt$ModifierLocalBeyondBoundsLayout$1.class", "size": 1324, "crc": -402441922}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayoutKt.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayoutKt.class", "size": 1530, "crc": -1057295576}, {"key": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt$lambda-1$1.class", "name": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt$lambda-1$1.class", "size": 2249, "crc": 1135044205}, {"key": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt.class", "name": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt.class", "size": 1526, "crc": -692576325}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Crop$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Crop$1.class", "size": 1405, "crc": -11188695}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillBounds$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillBounds$1.class", "size": 1354, "crc": -774898833}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillHeight$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillHeight$1.class", "size": 1417, "crc": 1756082959}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillWidth$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillWidth$1.class", "size": 1413, "crc": 495621407}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Fit$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Fit$1.class", "size": 1402, "crc": 173894681}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Inside$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Inside$1.class", "size": 1581, "crc": -811255016}, {"key": "androidx/compose/ui/layout/ContentScale$Companion.class", "name": "androidx/compose/ui/layout/ContentScale$Companion.class", "size": 3342, "crc": 1972243019}, {"key": "androidx/compose/ui/layout/ContentScale.class", "name": "androidx/compose/ui/layout/ContentScale.class", "size": 1025, "crc": 518050558}, {"key": "androidx/compose/ui/layout/ContentScaleKt.class", "name": "androidx/compose/ui/layout/ContentScaleKt.class", "size": 1924, "crc": 282617999}, {"key": "androidx/compose/ui/layout/DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/DefaultIntrinsicMeasurable.class", "size": 3574, "crc": 864178611}, {"key": "androidx/compose/ui/layout/FixedCountSubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/FixedCountSubcomposeSlotReusePolicy.class", "size": 2156, "crc": -1428618795}, {"key": "androidx/compose/ui/layout/FixedScale.class", "name": "androidx/compose/ui/layout/FixedScale.class", "size": 2653, "crc": 506484435}, {"key": "androidx/compose/ui/layout/FixedSizeIntrinsicsPlaceable.class", "name": "androidx/compose/ui/layout/FixedSizeIntrinsicsPlaceable.class", "size": 1851, "crc": -2105321959}, {"key": "androidx/compose/ui/layout/GraphicLayerInfo$DefaultImpls.class", "name": "androidx/compose/ui/layout/GraphicLayerInfo$DefaultImpls.class", "size": 791, "crc": -6461469}, {"key": "androidx/compose/ui/layout/GraphicLayerInfo.class", "name": "androidx/compose/ui/layout/GraphicLayerInfo.class", "size": 926, "crc": 1605072622}, {"key": "androidx/compose/ui/layout/HorizontalAlignmentLine.class", "name": "androidx/compose/ui/layout/HorizontalAlignmentLine.class", "size": 1243, "crc": 1279167964}, {"key": "androidx/compose/ui/layout/HorizontalRuler.class", "name": "androidx/compose/ui/layout/HorizontalRuler.class", "size": 1765, "crc": 586360174}, {"key": "androidx/compose/ui/layout/IntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/IntrinsicMeasurable.class", "size": 825, "crc": -634927249}, {"key": "androidx/compose/ui/layout/IntrinsicMeasureScope.class", "name": "androidx/compose/ui/layout/IntrinsicMeasureScope.class", "size": 929, "crc": -110326601}, {"key": "androidx/compose/ui/layout/IntrinsicMinMax.class", "name": "androidx/compose/ui/layout/IntrinsicMinMax.class", "size": 1389, "crc": 401931185}, {"key": "androidx/compose/ui/layout/IntrinsicWidthHeight.class", "name": "androidx/compose/ui/layout/IntrinsicWidthHeight.class", "size": 1424, "crc": -174690846}, {"key": "androidx/compose/ui/layout/IntrinsicsMeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/IntrinsicsMeasureScope$layout$1.class", "size": 2726, "crc": 1272341509}, {"key": "androidx/compose/ui/layout/IntrinsicsMeasureScope.class", "name": "androidx/compose/ui/layout/IntrinsicsMeasureScope.class", "size": 7864, "crc": 739420342}, {"key": "androidx/compose/ui/layout/LayoutCoordinates$DefaultImpls.class", "name": "androidx/compose/ui/layout/LayoutCoordinates$DefaultImpls.class", "size": 2758, "crc": 1715256051}, {"key": "androidx/compose/ui/layout/LayoutCoordinates.class", "name": "androidx/compose/ui/layout/LayoutCoordinates.class", "size": 6043, "crc": -1822787933}, {"key": "androidx/compose/ui/layout/LayoutCoordinatesKt.class", "name": "androidx/compose/ui/layout/LayoutCoordinatesKt.class", "size": 7016, "crc": -1054714158}, {"key": "androidx/compose/ui/layout/LayoutElement.class", "name": "androidx/compose/ui/layout/LayoutElement.class", "size": 5298, "crc": -256711872}, {"key": "androidx/compose/ui/layout/LayoutIdElement.class", "name": "androidx/compose/ui/layout/LayoutIdElement.class", "size": 3640, "crc": -2060630073}, {"key": "androidx/compose/ui/layout/LayoutIdKt.class", "name": "androidx/compose/ui/layout/LayoutIdKt.class", "size": 1541, "crc": -464828326}, {"key": "androidx/compose/ui/layout/LayoutIdModifier.class", "name": "androidx/compose/ui/layout/LayoutIdModifier.class", "size": 1940, "crc": -597712235}, {"key": "androidx/compose/ui/layout/LayoutIdParentData.class", "name": "androidx/compose/ui/layout/LayoutIdParentData.class", "size": 566, "crc": -894342838}, {"key": "androidx/compose/ui/layout/LayoutInfo.class", "name": "androidx/compose/ui/layout/LayoutInfo.class", "size": 2060, "crc": 1548352715}, {"key": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$1$1.class", "name": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$1$1.class", "size": 1653, "crc": 744194075}, {"key": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$2.class", "name": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$2.class", "size": 2388, "crc": 819452292}, {"key": "androidx/compose/ui/layout/LayoutKt$combineAsVirtualLayouts$1.class", "name": "androidx/compose/ui/layout/LayoutKt$combineAsVirtualLayouts$1.class", "size": 6502, "crc": -1829285972}, {"key": "androidx/compose/ui/layout/LayoutKt$materializerOf$1.class", "name": "androidx/compose/ui/layout/LayoutKt$materializerOf$1.class", "size": 5468, "crc": 1974649248}, {"key": "androidx/compose/ui/layout/LayoutKt$materializerOfWithCompositionLocalInjection$1.class", "name": "androidx/compose/ui/layout/LayoutKt$materializerOfWithCompositionLocalInjection$1.class", "size": 5721, "crc": -1333532633}, {"key": "androidx/compose/ui/layout/LayoutKt.class", "name": "androidx/compose/ui/layout/LayoutKt.class", "size": 16007, "crc": 1096254521}, {"key": "androidx/compose/ui/layout/LayoutModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/LayoutModifier$DefaultImpls.class", "size": 3565, "crc": -1307226928}, {"key": "androidx/compose/ui/layout/LayoutModifier.class", "name": "androidx/compose/ui/layout/LayoutModifier.class", "size": 4567, "crc": -252721231}, {"key": "androidx/compose/ui/layout/LayoutModifierImpl.class", "name": "androidx/compose/ui/layout/LayoutModifierImpl.class", "size": 3575, "crc": -588562340}, {"key": "androidx/compose/ui/layout/LayoutModifierKt.class", "name": "androidx/compose/ui/layout/LayoutModifierKt.class", "size": 1618, "crc": -823989386}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$NodeState.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$NodeState.class", "size": 4946, "crc": 1024054752}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$PostLookaheadMeasureScopeImpl.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$PostLookaheadMeasureScopeImpl.class", "size": 8000, "crc": 1709729609}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope$layout$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope$layout$1.class", "size": 4604, "crc": -457991666}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope.class", "size": 6984, "crc": 1104650336}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$1.class", "size": 4013, "crc": 13379105}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$2.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$2.class", "size": 4042, "crc": -141026772}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1.class", "size": 6286, "crc": 1197}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasureResult$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasureResult$1.class", "size": 3066, "crc": 1446430933}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$disposeUnusedSlotsInPostLookahead$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$disposeUnusedSlotsInPostLookahead$1.class", "size": 2907, "crc": -2094145428}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precompose$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precompose$1.class", "size": 1174, "crc": -250186661}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precompose$2.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precompose$2.class", "size": 7599, "crc": -1705686339}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$subcompose$3$1$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$subcompose$3$1$1.class", "size": 4549, "crc": -1065543362}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState.class", "size": 36809, "crc": -1324093765}, {"key": "androidx/compose/ui/layout/LookaheadCapablePlacementScope.class", "name": "androidx/compose/ui/layout/LookaheadCapablePlacementScope.class", "size": 2684, "crc": 1767558585}, {"key": "androidx/compose/ui/layout/LookaheadLayoutCoordinates.class", "name": "androidx/compose/ui/layout/LookaheadLayoutCoordinates.class", "size": 11032, "crc": -1542792585}, {"key": "androidx/compose/ui/layout/LookaheadLayoutCoordinatesKt.class", "name": "androidx/compose/ui/layout/LookaheadLayoutCoordinatesKt.class", "size": 1681, "crc": -1470270002}, {"key": "androidx/compose/ui/layout/LookaheadScope.class", "name": "androidx/compose/ui/layout/LookaheadScope.class", "size": 2803, "crc": -1369432179}, {"key": "androidx/compose/ui/layout/LookaheadScopeImpl.class", "name": "androidx/compose/ui/layout/LookaheadScopeImpl.class", "size": 3812, "crc": 1360590872}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$1.class", "size": 1453, "crc": -517356765}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$1.class", "size": 1605, "crc": 430815861}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2$1.class", "size": 1813, "crc": 2009746724}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2.class", "size": 2078, "crc": 1511770482}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$4.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$4.class", "size": 2044, "crc": 710345975}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$defaultPlacementApproachInProgress$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$defaultPlacementApproachInProgress$1.class", "size": 1985, "crc": 1641443129}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt.class", "size": 11117, "crc": 412668610}, {"key": "androidx/compose/ui/layout/Measurable.class", "name": "androidx/compose/ui/layout/Measurable.class", "size": 812, "crc": -1021442073}, {"key": "androidx/compose/ui/layout/MeasurePolicy$DefaultImpls.class", "name": "androidx/compose/ui/layout/MeasurePolicy$DefaultImpls.class", "size": 1834, "crc": 2119396127}, {"key": "androidx/compose/ui/layout/MeasurePolicy.class", "name": "androidx/compose/ui/layout/MeasurePolicy.class", "size": 8340, "crc": 813256788}, {"key": "androidx/compose/ui/layout/MeasureResult.class", "name": "androidx/compose/ui/layout/MeasureResult.class", "size": 1418, "crc": -706838621}, {"key": "androidx/compose/ui/layout/MeasureScope$DefaultImpls.class", "name": "androidx/compose/ui/layout/MeasureScope$DefaultImpls.class", "size": 5696, "crc": -104352630}, {"key": "androidx/compose/ui/layout/MeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/MeasureScope$layout$1.class", "size": 3934, "crc": -552200710}, {"key": "androidx/compose/ui/layout/MeasureScope.class", "name": "androidx/compose/ui/layout/MeasureScope.class", "size": 8436, "crc": -17677039}, {"key": "androidx/compose/ui/layout/MeasureScopeMarker.class", "name": "androidx/compose/ui/layout/MeasureScopeMarker.class", "size": 594, "crc": -1651354876}, {"key": "androidx/compose/ui/layout/Measured.class", "name": "androidx/compose/ui/layout/Measured.class", "size": 1070, "crc": -1331784956}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "size": 4118, "crc": -412310346}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$EmptyPlaceable.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$EmptyPlaceable.class", "size": 1971, "crc": 798786478}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicMinMax.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicMinMax.class", "size": 1618, "crc": -169172764}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicWidthHeight.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicWidthHeight.class", "size": 1658, "crc": 435579126}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics.class", "size": 4743, "crc": 2097980555}, {"key": "androidx/compose/ui/layout/ModifierInfo.class", "name": "androidx/compose/ui/layout/ModifierInfo.class", "size": 2412, "crc": -1546001803}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicy.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicy.class", "size": 10665, "crc": 1201465161}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicyImpl.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicyImpl.class", "size": 5880, "crc": 1179512947}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicyKt.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicyKt.class", "size": 1080, "crc": 1235254069}, {"key": "androidx/compose/ui/layout/NoOpSubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/NoOpSubcomposeSlotReusePolicy.class", "size": 1614, "crc": -27025158}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedElement.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedElement.class", "size": 3561, "crc": 219086767}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifier$DefaultImpls.class", "size": 2591, "crc": 1193933673}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifier.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifier.class", "size": 2275, "crc": -1030432424}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifierKt.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifierKt.class", "size": 1419, "crc": 519564956}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedNode.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedNode.class", "size": 2126, "crc": 592518463}, {"key": "androidx/compose/ui/layout/OnPlacedElement.class", "name": "androidx/compose/ui/layout/OnPlacedElement.class", "size": 4602, "crc": 1451102080}, {"key": "androidx/compose/ui/layout/OnPlacedModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnPlacedModifier$DefaultImpls.class", "size": 2471, "crc": 958161903}, {"key": "androidx/compose/ui/layout/OnPlacedModifier.class", "name": "androidx/compose/ui/layout/OnPlacedModifier.class", "size": 2179, "crc": -294563801}, {"key": "androidx/compose/ui/layout/OnPlacedModifierKt.class", "name": "androidx/compose/ui/layout/OnPlacedModifierKt.class", "size": 1359, "crc": 1772223665}, {"key": "androidx/compose/ui/layout/OnPlacedNode.class", "name": "androidx/compose/ui/layout/OnPlacedNode.class", "size": 2062, "crc": 465588010}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifier$DefaultImpls.class", "size": 2511, "crc": 167791955}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifier.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifier.class", "size": 2107, "crc": -740990325}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifierKt.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifierKt.class", "size": 1359, "crc": -2051859248}, {"key": "androidx/compose/ui/layout/OnSizeChangedModifier.class", "name": "androidx/compose/ui/layout/OnSizeChangedModifier.class", "size": 3199, "crc": -1017705416}, {"key": "androidx/compose/ui/layout/OnSizeChangedNode.class", "name": "androidx/compose/ui/layout/OnSizeChangedNode.class", "size": 2362, "crc": 1623212180}, {"key": "androidx/compose/ui/layout/OuterPlacementScope.class", "name": "androidx/compose/ui/layout/OuterPlacementScope.class", "size": 2133, "crc": -135631240}, {"key": "androidx/compose/ui/layout/ParentDataModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/ParentDataModifier$DefaultImpls.class", "size": 2491, "crc": -744915144}, {"key": "androidx/compose/ui/layout/ParentDataModifier.class", "name": "androidx/compose/ui/layout/ParentDataModifier.class", "size": 2305, "crc": -1691638151}, {"key": "androidx/compose/ui/layout/PinnableContainer$PinnedHandle.class", "name": "androidx/compose/ui/layout/PinnableContainer$PinnedHandle.class", "size": 578, "crc": 2035097255}, {"key": "androidx/compose/ui/layout/PinnableContainer.class", "name": "androidx/compose/ui/layout/PinnableContainer.class", "size": 812, "crc": -396088345}, {"key": "androidx/compose/ui/layout/PinnableContainerKt$LocalPinnableContainer$1.class", "name": "androidx/compose/ui/layout/PinnableContainerKt$LocalPinnableContainer$1.class", "size": 1299, "crc": 1088410326}, {"key": "androidx/compose/ui/layout/PinnableContainerKt.class", "name": "androidx/compose/ui/layout/PinnableContainerKt.class", "size": 1563, "crc": -713886885}, {"key": "androidx/compose/ui/layout/Placeable$PlacementScope.class", "name": "androidx/compose/ui/layout/Placeable$PlacementScope.class", "size": 17936, "crc": -1873778368}, {"key": "androidx/compose/ui/layout/Placeable.class", "name": "androidx/compose/ui/layout/Placeable.class", "size": 5089, "crc": 207697551}, {"key": "androidx/compose/ui/layout/PlaceableKt$DefaultLayerBlock$1.class", "name": "androidx/compose/ui/layout/PlaceableKt$DefaultLayerBlock$1.class", "size": 1480, "crc": 362485294}, {"key": "androidx/compose/ui/layout/PlaceableKt.class", "name": "androidx/compose/ui/layout/PlaceableKt.class", "size": 2563, "crc": -1939742634}, {"key": "androidx/compose/ui/layout/PlacementScopeMarker.class", "name": "androidx/compose/ui/layout/PlacementScopeMarker.class", "size": 606, "crc": 2116422081}, {"key": "androidx/compose/ui/layout/Remeasurement.class", "name": "androidx/compose/ui/layout/Remeasurement.class", "size": 455, "crc": 2104846407}, {"key": "androidx/compose/ui/layout/RemeasurementModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/RemeasurementModifier$DefaultImpls.class", "size": 2521, "crc": 42175298}, {"key": "androidx/compose/ui/layout/RemeasurementModifier.class", "name": "androidx/compose/ui/layout/RemeasurementModifier.class", "size": 2224, "crc": 430016064}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$1.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$1.class", "size": 1699, "crc": 1568613652}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$2.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$2.class", "size": 1961, "crc": 814175796}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$4.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$4.class", "size": 3269, "crc": -1866902560}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy.class", "size": 5559, "crc": -1469586883}, {"key": "androidx/compose/ui/layout/Ruler.class", "name": "androidx/compose/ui/layout/Ruler.class", "size": 1420, "crc": -2011636576}, {"key": "androidx/compose/ui/layout/RulerScope.class", "name": "androidx/compose/ui/layout/RulerScope.class", "size": 1166, "crc": -145381101}, {"key": "androidx/compose/ui/layout/ScaleFactor$Companion.class", "name": "androidx/compose/ui/layout/ScaleFactor$Companion.class", "size": 1252, "crc": 1600885310}, {"key": "androidx/compose/ui/layout/ScaleFactor.class", "name": "androidx/compose/ui/layout/ScaleFactor.class", "size": 6344, "crc": 330507140}, {"key": "androidx/compose/ui/layout/ScaleFactorKt.class", "name": "androidx/compose/ui/layout/ScaleFactorKt.class", "size": 4874, "crc": 2088008631}, {"key": "androidx/compose/ui/layout/SimplePlacementScope.class", "name": "androidx/compose/ui/layout/SimplePlacementScope.class", "size": 1401, "crc": -1667101785}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$ReusedSlotId$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$ReusedSlotId$1.class", "size": 883, "crc": 680017904}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$2.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$2.class", "size": 2331, "crc": 2037324028}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$4$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$4$1.class", "size": 1484, "crc": 920084136}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$5.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$5.class", "size": 2581, "crc": 1759279968}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt.class", "size": 11879, "crc": -440911282}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$PrecomposedSlotHandle.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$PrecomposedSlotHandle.class", "size": 2095, "crc": 984701745}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setCompositionContext$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setCompositionContext$1.class", "size": 2190, "crc": 1201053017}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setMeasurePolicy$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setMeasurePolicy$1.class", "size": 2800, "crc": 1497543152}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setRoot$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setRoot$1.class", "size": 2967, "crc": -1552560670}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState.class", "size": 6932, "crc": -1403816962}, {"key": "androidx/compose/ui/layout/SubcomposeMeasureScope.class", "name": "androidx/compose/ui/layout/SubcomposeMeasureScope.class", "size": 1215, "crc": -878870699}, {"key": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy$SlotIdsSet.class", "name": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy$SlotIdsSet.class", "size": 4963, "crc": -2066463919}, {"key": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy.class", "size": 1050, "crc": 349090323}, {"key": "androidx/compose/ui/layout/TestModifierUpdater.class", "name": "androidx/compose/ui/layout/TestModifierUpdater.class", "size": 1697, "crc": -894707166}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$1$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$1$1.class", "size": 1943, "crc": -1061718434}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$2.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$2.class", "size": 2000, "crc": 162116906}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1$1.class", "size": 1831, "crc": -1875005056}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1.class", "size": 2368, "crc": -1970929062}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt.class", "size": 6958, "crc": -1274984336}, {"key": "androidx/compose/ui/layout/VerticalAlignmentLine.class", "name": "androidx/compose/ui/layout/VerticalAlignmentLine.class", "size": 1239, "crc": -1104549621}, {"key": "androidx/compose/ui/layout/VerticalRuler.class", "name": "androidx/compose/ui/layout/VerticalRuler.class", "size": 1762, "crc": -1568572276}, {"key": "androidx/compose/ui/modifier/BackwardsCompatLocalMap.class", "name": "androidx/compose/ui/modifier/BackwardsCompatLocalMap.class", "size": 4252, "crc": 914263596}, {"key": "androidx/compose/ui/modifier/EmptyMap.class", "name": "androidx/compose/ui/modifier/EmptyMap.class", "size": 2384, "crc": 2027740431}, {"key": "androidx/compose/ui/modifier/ModifierLocal.class", "name": "androidx/compose/ui/modifier/ModifierLocal.class", "size": 1611, "crc": 1850995030}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumer$DefaultImpls.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumer$DefaultImpls.class", "size": 2539, "crc": -539948061}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumer.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumer.class", "size": 2327, "crc": -1211277951}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerImpl.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerImpl.class", "size": 2770, "crc": -437257652}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerKt$modifierLocalConsumer$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerKt$modifierLocalConsumer$$inlined$debugInspectorInfo$1.class", "size": 3029, "crc": 313596957}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerKt.class", "size": 2882, "crc": -1839841775}, {"key": "androidx/compose/ui/modifier/ModifierLocalKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalKt.class", "size": 1122, "crc": -67739367}, {"key": "androidx/compose/ui/modifier/ModifierLocalManager$invalidate$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalManager$invalidate$1.class", "size": 1260, "crc": -549387263}, {"key": "androidx/compose/ui/modifier/ModifierLocalManager.class", "name": "androidx/compose/ui/modifier/ModifierLocalManager.class", "size": 14846, "crc": -1972871285}, {"key": "androidx/compose/ui/modifier/ModifierLocalMap.class", "name": "androidx/compose/ui/modifier/ModifierLocalMap.class", "size": 2102, "crc": -60053819}, {"key": "androidx/compose/ui/modifier/ModifierLocalModifierNode.class", "name": "androidx/compose/ui/modifier/ModifierLocalModifierNode.class", "size": 9684, "crc": -518921182}, {"key": "androidx/compose/ui/modifier/ModifierLocalModifierNodeKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalModifierNodeKt.class", "size": 8211, "crc": -**********}, {"key": "androidx/compose/ui/modifier/ModifierLocalProvider$DefaultImpls.class", "name": "androidx/compose/ui/modifier/ModifierLocalProvider$DefaultImpls.class", "size": 2768, "crc": -137759420}, {"key": "androidx/compose/ui/modifier/ModifierLocalProvider.class", "name": "androidx/compose/ui/modifier/ModifierLocalProvider.class", "size": 2559, "crc": -383745805}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$$inlined$debugInspectorInfo$1.class", "size": 3244, "crc": -**********}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$1.class", "size": 3711, "crc": -**********}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt.class", "size": 3145, "crc": -**********}, {"key": "androidx/compose/ui/modifier/ModifierLocalReadScope.class", "name": "androidx/compose/ui/modifier/ModifierLocalReadScope.class", "size": 816, "crc": -676506730}, {"key": "androidx/compose/ui/modifier/MultiLocalMap.class", "name": "androidx/compose/ui/modifier/MultiLocalMap.class", "size": 3449, "crc": 138549821}, {"key": "androidx/compose/ui/modifier/ProvidableModifierLocal.class", "name": "androidx/compose/ui/modifier/ProvidableModifierLocal.class", "size": 1250, "crc": **********}, {"key": "androidx/compose/ui/modifier/SingleLocalMap.class", "name": "androidx/compose/ui/modifier/SingleLocalMap.class", "size": 4906, "crc": -**********}, {"key": "androidx/compose/ui/node/AlignmentLines$recalculate$1.class", "name": "androidx/compose/ui/node/AlignmentLines$recalculate$1.class", "size": 5033, "crc": **********}, {"key": "androidx/compose/ui/node/AlignmentLines.class", "name": "androidx/compose/ui/node/AlignmentLines.class", "size": 9947, "crc": 955034980}, {"key": "androidx/compose/ui/node/AlignmentLinesOwner.class", "name": "androidx/compose/ui/node/AlignmentLinesOwner.class", "size": 1926, "crc": 878311003}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$2.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$2.class", "size": 1278, "crc": -1815088090}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$3.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$3.class", "size": 2662, "crc": -348894039}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$updateDrawCache$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$updateDrawCache$1.class", "size": 1632, "crc": -2065998470}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$updateModifierLocalConsumer$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$updateModifierLocalConsumer$1.class", "size": 1847, "crc": -1080533989}, {"key": "androidx/compose/ui/node/BackwardsCompatNode.class", "name": "androidx/compose/ui/node/BackwardsCompatNode.class", "size": 31216, "crc": 1202335445}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$DetachedModifierLocalReadScope$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$DetachedModifierLocalReadScope$1.class", "size": 1513, "crc": -2138172769}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$onDrawCacheReadsChanged$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$onDrawCacheReadsChanged$1.class", "size": 1571, "crc": -930411754}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$updateModifierLocalConsumer$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$updateModifierLocalConsumer$1.class", "size": 1572, "crc": -1824945403}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt.class", "size": 3231, "crc": -1299487937}, {"key": "androidx/compose/ui/node/CanFocusChecker.class", "name": "androidx/compose/ui/node/CanFocusChecker.class", "size": 2615, "crc": -156678485}, {"key": "androidx/compose/ui/node/CenteredArray.class", "name": "androidx/compose/ui/node/CenteredArray.class", "size": 2733, "crc": -1388257140}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetCompositeKeyHash$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetCompositeKeyHash$1.class", "size": 1692, "crc": 1422239418}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetDensity$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetDensity$1.class", "size": 1776, "crc": 1213204030}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetLayoutDirection$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetLayoutDirection$1.class", "size": 1840, "crc": 268734300}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetMeasurePolicy$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetMeasurePolicy$1.class", "size": 1834, "crc": 1004672900}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetModifier$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetModifier$1.class", "size": 1759, "crc": -1963114970}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetResolvedCompositionLocals$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetResolvedCompositionLocals$1.class", "size": 1884, "crc": 958530164}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetViewConfiguration$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetViewConfiguration$1.class", "size": 1876, "crc": 1760963134}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$VirtualConstructor$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$VirtualConstructor$1.class", "size": 1443, "crc": 577046882}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion.class", "size": 7157, "crc": 497607607}, {"key": "androidx/compose/ui/node/ComposeUiNode.class", "name": "androidx/compose/ui/node/ComposeUiNode.class", "size": 2810, "crc": -1035571057}, {"key": "androidx/compose/ui/node/CompositionLocalConsumerModifierNode.class", "name": "androidx/compose/ui/node/CompositionLocalConsumerModifierNode.class", "size": 547, "crc": -513795697}, {"key": "androidx/compose/ui/node/CompositionLocalConsumerModifierNodeKt.class", "name": "androidx/compose/ui/node/CompositionLocalConsumerModifierNodeKt.class", "size": 3240, "crc": 1671144488}, {"key": "androidx/compose/ui/node/DelegatableNode.class", "name": "androidx/compose/ui/node/DelegatableNode.class", "size": 731, "crc": -625781292}, {"key": "androidx/compose/ui/node/DelegatableNodeKt.class", "name": "androidx/compose/ui/node/DelegatableNodeKt.class", "size": 55263, "crc": -1028169379}, {"key": "androidx/compose/ui/node/DelegatableNode_androidKt.class", "name": "androidx/compose/ui/node/DelegatableNode_androidKt.class", "size": 2724, "crc": 1876632468}, {"key": "androidx/compose/ui/node/DelegatingNode.class", "name": "androidx/compose/ui/node/DelegatingNode.class", "size": 12584, "crc": -558368842}, {"key": "androidx/compose/ui/node/DepthSortedSet$DepthComparator$1.class", "name": "androidx/compose/ui/node/DepthSortedSet$DepthComparator$1.class", "size": 1651, "crc": -1476817403}, {"key": "androidx/compose/ui/node/DepthSortedSet$mapOfOriginalDepth$2.class", "name": "androidx/compose/ui/node/DepthSortedSet$mapOfOriginalDepth$2.class", "size": 1444, "crc": 1661075280}, {"key": "androidx/compose/ui/node/DepthSortedSet.class", "name": "androidx/compose/ui/node/DepthSortedSet.class", "size": 6858, "crc": -1812745657}, {"key": "androidx/compose/ui/node/DepthSortedSetsForDifferentPasses.class", "name": "androidx/compose/ui/node/DepthSortedSetsForDifferentPasses.class", "size": 5005, "crc": -1837026551}, {"key": "androidx/compose/ui/node/DiffCallback.class", "name": "androidx/compose/ui/node/DiffCallback.class", "size": 662, "crc": -1759084251}, {"key": "androidx/compose/ui/node/DistanceAndInLayer.class", "name": "androidx/compose/ui/node/DistanceAndInLayer.class", "size": 4096, "crc": 1824520186}, {"key": "androidx/compose/ui/node/DrawModifierNode.class", "name": "androidx/compose/ui/node/DrawModifierNode.class", "size": 909, "crc": 1636869830}, {"key": "androidx/compose/ui/node/DrawModifierNodeKt.class", "name": "androidx/compose/ui/node/DrawModifierNodeKt.class", "size": 2102, "crc": -440605631}, {"key": "androidx/compose/ui/node/GlobalPositionAwareModifierNode.class", "name": "androidx/compose/ui/node/GlobalPositionAwareModifierNode.class", "size": 815, "crc": -7922289}, {"key": "androidx/compose/ui/node/HitTestResult$HitTestResultIterator.class", "name": "androidx/compose/ui/node/HitTestResult$HitTestResultIterator.class", "size": 3834, "crc": -220940652}, {"key": "androidx/compose/ui/node/HitTestResult$SubList.class", "name": "androidx/compose/ui/node/HitTestResult$SubList.class", "size": 8570, "crc": 2044003239}, {"key": "androidx/compose/ui/node/HitTestResult.class", "name": "androidx/compose/ui/node/HitTestResult.class", "size": 13469, "crc": -1819603125}, {"key": "androidx/compose/ui/node/HitTestResultKt.class", "name": "androidx/compose/ui/node/HitTestResultKt.class", "size": 973, "crc": -920379195}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator$Companion.class", "size": 1191, "crc": 2031525749}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator$LookaheadDelegateImpl.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator$LookaheadDelegateImpl.class", "size": 7201, "crc": -1790775901}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator.class", "size": 14622, "crc": -1976883499}, {"key": "androidx/compose/ui/node/IntStack.class", "name": "androidx/compose/ui/node/IntStack.class", "size": 4354, "crc": -517994871}, {"key": "androidx/compose/ui/node/InternalCoreApi.class", "name": "androidx/compose/ui/node/InternalCoreApi.class", "size": 1014, "crc": -191478048}, {"key": "androidx/compose/ui/node/InteroperableComposeUiNode.class", "name": "androidx/compose/ui/node/InteroperableComposeUiNode.class", "size": 775, "crc": 176143914}, {"key": "androidx/compose/ui/node/IntrinsicsPolicy.class", "name": "androidx/compose/ui/node/IntrinsicsPolicy.class", "size": 6087, "crc": 1395679410}, {"key": "androidx/compose/ui/node/LayerPositionalProperties.class", "name": "androidx/compose/ui/node/LayerPositionalProperties.class", "size": 2957, "crc": -1223286644}, {"key": "androidx/compose/ui/node/LayoutAwareModifierNode.class", "name": "androidx/compose/ui/node/LayoutAwareModifierNode.class", "size": 1113, "crc": 2004865136}, {"key": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicHeight$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicHeight$1.class", "size": 1779, "crc": 2114976442}, {"key": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicWidth$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicWidth$1.class", "size": 1775, "crc": -430272884}, {"key": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicHeight$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicHeight$1.class", "size": 1779, "crc": -402965840}, {"key": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicWidth$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicWidth$1.class", "size": 1775, "crc": 1965423876}, {"key": "androidx/compose/ui/node/LayoutModifierNode.class", "name": "androidx/compose/ui/node/LayoutModifierNode.class", "size": 3258, "crc": -1050771022}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$Companion.class", "size": 1236, "crc": -1644183495}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$LookaheadDelegateForLayoutModifierNode.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$LookaheadDelegateForLayoutModifierNode.class", "size": 6812, "crc": 1123848754}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$measure$1$1$1$1.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$measure$1$1$1$1.class", "size": 2628, "crc": -507420791}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator.class", "size": 19143, "crc": 583347171}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinatorKt.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinatorKt.class", "size": 3652, "crc": -1121839139}, {"key": "androidx/compose/ui/node/LayoutModifierNodeKt.class", "name": "androidx/compose/ui/node/LayoutModifierNodeKt.class", "size": 2863, "crc": -1563915647}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$Constructor$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$Constructor$1.class", "size": 1302, "crc": -1960531057}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$DummyViewConfiguration$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$DummyViewConfiguration$1.class", "size": 1808, "crc": -1853589435}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$ErrorMeasurePolicy$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$ErrorMeasurePolicy$1.class", "size": 2117, "crc": -1494838831}, {"key": "androidx/compose/ui/node/LayoutNode$Companion.class", "name": "androidx/compose/ui/node/LayoutNode$Companion.class", "size": 2216, "crc": -708791862}, {"key": "androidx/compose/ui/node/LayoutNode$LayoutState.class", "name": "androidx/compose/ui/node/LayoutNode$LayoutState.class", "size": 1727, "crc": 1950858505}, {"key": "androidx/compose/ui/node/LayoutNode$NoIntrinsicsMeasurePolicy.class", "name": "androidx/compose/ui/node/LayoutNode$NoIntrinsicsMeasurePolicy.class", "size": 3172, "crc": 25649286}, {"key": "androidx/compose/ui/node/LayoutNode$UsageByParent.class", "name": "androidx/compose/ui/node/LayoutNode$UsageByParent.class", "size": 1609, "crc": 514018120}, {"key": "androidx/compose/ui/node/LayoutNode$WhenMappings.class", "name": "androidx/compose/ui/node/LayoutNode$WhenMappings.class", "size": 794, "crc": 891043233}, {"key": "androidx/compose/ui/node/LayoutNode$_foldedChildren$1.class", "name": "androidx/compose/ui/node/LayoutNode$_foldedChildren$1.class", "size": 1347, "crc": -840830810}, {"key": "androidx/compose/ui/node/LayoutNode$collapsedSemantics$1.class", "name": "androidx/compose/ui/node/LayoutNode$collapsedSemantics$1.class", "size": 7932, "crc": 2072269091}, {"key": "androidx/compose/ui/node/LayoutNode.class", "name": "androidx/compose/ui/node/LayoutNode.class", "size": 85656, "crc": -160218470}, {"key": "androidx/compose/ui/node/LayoutNodeAlignmentLines.class", "name": "androidx/compose/ui/node/LayoutNodeAlignmentLines.class", "size": 2758, "crc": -1678141680}, {"key": "androidx/compose/ui/node/LayoutNodeDrawScope.class", "name": "androidx/compose/ui/node/LayoutNodeDrawScope.class", "size": 28171, "crc": -73650265}, {"key": "androidx/compose/ui/node/LayoutNodeDrawScopeKt.class", "name": "androidx/compose/ui/node/LayoutNodeDrawScopeKt.class", "size": 2306, "crc": -861680923}, {"key": "androidx/compose/ui/node/LayoutNodeKt.class", "name": "androidx/compose/ui/node/LayoutNodeKt.class", "size": 2995, "crc": -463805776}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$WhenMappings.class", "size": 1542, "crc": -278385502}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$layoutChildren$1$1.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$layoutChildren$1$1.class", "size": 1970, "crc": 874669026}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$layoutChildren$1$4.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$layoutChildren$1$4.class", "size": 2049, "crc": 1936166750}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$layoutChildren$1.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$layoutChildren$1.class", "size": 5581, "crc": -736467592}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$placeSelf$2.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$placeSelf$2.class", "size": 3114, "crc": 1916591834}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$remeasure$2.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate$remeasure$2.class", "size": 1883, "crc": -13965517}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$LookaheadPassDelegate.class", "size": 36089, "crc": -1127265364}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$WhenMappings.class", "size": 1420, "crc": 324017261}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildrenBlock$1$1.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildrenBlock$1$1.class", "size": 1972, "crc": 863427887}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildrenBlock$1$2.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildrenBlock$1$2.class", "size": 2051, "crc": -1234358543}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildrenBlock$1.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$layoutChildrenBlock$1.class", "size": 2472, "crc": -1323974737}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinatorBlock$1.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$placeOuterCoordinatorBlock$1.class", "size": 3979, "crc": -747852736}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$remeasure$2.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate$remeasure$2.class", "size": 1875, "crc": 1751718422}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$MeasurePassDelegate.class", "size": 41546, "crc": -886491440}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$performLookaheadMeasure$1.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$performLookaheadMeasure$1.class", "size": 1775, "crc": -1803161221}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$performMeasureBlock$1.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate$performMeasureBlock$1.class", "size": 1604, "crc": 1377502740}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate.class", "size": 16797, "crc": -124740294}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegateKt.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegateKt.class", "size": 4560, "crc": 392961111}, {"key": "androidx/compose/ui/node/LayoutTreeConsistencyChecker.class", "name": "androidx/compose/ui/node/LayoutTreeConsistencyChecker.class", "size": 9555, "crc": 532608826}, {"key": "androidx/compose/ui/node/LookaheadAlignmentLines.class", "name": "androidx/compose/ui/node/LookaheadAlignmentLines.class", "size": 4006, "crc": 1831216609}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion$onCommitAffectingRuler$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion$onCommitAffectingRuler$1.class", "size": 1831, "crc": 1382035819}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion.class", "size": 1008, "crc": 132163749}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$captureRulers$3.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$captureRulers$3.class", "size": 1867, "crc": -267467812}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$layout$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$layout$1.class", "size": 3588, "crc": 1237250901}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$rulerScope$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$rulerScope$1.class", "size": 2613, "crc": 1906073389}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable.class", "size": 29534, "crc": 407368961}, {"key": "androidx/compose/ui/node/LookaheadDelegate.class", "name": "androidx/compose/ui/node/LookaheadDelegate.class", "size": 13506, "crc": -2048650878}, {"key": "androidx/compose/ui/node/LookaheadDelegateKt.class", "name": "androidx/compose/ui/node/LookaheadDelegateKt.class", "size": 2172, "crc": -1468886359}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate$PostponedRequest.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate$PostponedRequest.class", "size": 1588, "crc": -101092863}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate$WhenMappings.class", "size": 1091, "crc": -519476276}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate.class", "size": 29507, "crc": -649849944}, {"key": "androidx/compose/ui/node/MeasureBlocks.class", "name": "androidx/compose/ui/node/MeasureBlocks.class", "size": 2076, "crc": -13826326}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNode.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNode.class", "size": 756, "crc": -899226696}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt$WhenMappings.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt$WhenMappings.class", "size": 1101, "crc": 262911529}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt.class", "size": 4652, "crc": -1665767671}, {"key": "androidx/compose/ui/node/MergedViewAdapter.class", "name": "androidx/compose/ui/node/MergedViewAdapter.class", "size": 5245, "crc": -176506219}, {"key": "androidx/compose/ui/node/ModifierNodeElement.class", "name": "androidx/compose/ui/node/ModifierNodeElement.class", "size": 4464, "crc": -1244763996}, {"key": "androidx/compose/ui/node/MotionReferencePlacementDelegate.class", "name": "androidx/compose/ui/node/MotionReferencePlacementDelegate.class", "size": 595, "crc": -1590106849}, {"key": "androidx/compose/ui/node/MutableVectorWithMutationTracking.class", "name": "androidx/compose/ui/node/MutableVectorWithMutationTracking.class", "size": 4973, "crc": -750150383}, {"key": "androidx/compose/ui/node/MyersDiffKt.class", "name": "androidx/compose/ui/node/MyersDiffKt.class", "size": 6301, "crc": -625496380}, {"key": "androidx/compose/ui/node/NestedVectorStack.class", "name": "androidx/compose/ui/node/NestedVectorStack.class", "size": 3735, "crc": -672514999}, {"key": "androidx/compose/ui/node/NodeChain$Differ.class", "name": "androidx/compose/ui/node/NodeChain$Differ.class", "size": 10102, "crc": 717192420}, {"key": "androidx/compose/ui/node/NodeChain$Logger.class", "name": "androidx/compose/ui/node/NodeChain$Logger.class", "size": 1862, "crc": -1357668752}, {"key": "androidx/compose/ui/node/NodeChain.class", "name": "androidx/compose/ui/node/NodeChain.class", "size": 43331, "crc": 1533813287}, {"key": "androidx/compose/ui/node/NodeChainKt$SentinelHead$1.class", "name": "androidx/compose/ui/node/NodeChainKt$SentinelHead$1.class", "size": 946, "crc": 1256307395}, {"key": "androidx/compose/ui/node/NodeChainKt$fillVector$1.class", "name": "androidx/compose/ui/node/NodeChainKt$fillVector$1.class", "size": 2104, "crc": 442007345}, {"key": "androidx/compose/ui/node/NodeChainKt.class", "name": "androidx/compose/ui/node/NodeChainKt.class", "size": 5749, "crc": 514410032}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$PointerInputSource$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$PointerInputSource$1.class", "size": 6489, "crc": 1399037899}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$SemanticsSource$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$SemanticsSource$1.class", "size": 3652, "crc": 883838216}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayer$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayer$1.class", "size": 1636, "crc": -762171729}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayerParams$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayerParams$1.class", "size": 3579, "crc": 774788307}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion.class", "size": 2151, "crc": -1299282343}, {"key": "androidx/compose/ui/node/NodeCoordinator$HitTestSource.class", "name": "androidx/compose/ui/node/NodeCoordinator$HitTestSource.class", "size": 1628, "crc": 1138828814}, {"key": "androidx/compose/ui/node/NodeCoordinator$drawBlock$1$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$drawBlock$1$1.class", "size": 1660, "crc": 1097612349}, {"key": "androidx/compose/ui/node/NodeCoordinator$drawBlock$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$drawBlock$1.class", "size": 2933, "crc": -1465989084}, {"key": "androidx/compose/ui/node/NodeCoordinator$hit$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$hit$1.class", "size": 3237, "crc": 1976275328}, {"key": "androidx/compose/ui/node/NodeCoordinator$hitNear$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$hitNear$1.class", "size": 3357, "crc": 1444597961}, {"key": "androidx/compose/ui/node/NodeCoordinator$invalidateParentLayer$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$invalidateParentLayer$1.class", "size": 1390, "crc": 410063152}, {"key": "androidx/compose/ui/node/NodeCoordinator$speculativeHit$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$speculativeHit$1.class", "size": 3420, "crc": 1787600369}, {"key": "androidx/compose/ui/node/NodeCoordinator$updateLayerParameters$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$updateLayerParameters$1.class", "size": 1725, "crc": -373068903}, {"key": "androidx/compose/ui/node/NodeCoordinator.class", "name": "androidx/compose/ui/node/NodeCoordinator.class", "size": 74582, "crc": 1554933731}, {"key": "androidx/compose/ui/node/NodeCoordinatorKt.class", "name": "androidx/compose/ui/node/NodeCoordinatorKt.class", "size": 1512, "crc": -152751202}, {"key": "androidx/compose/ui/node/NodeKind.class", "name": "androidx/compose/ui/node/NodeKind.class", "size": 2532, "crc": -1221970355}, {"key": "androidx/compose/ui/node/NodeKindKt.class", "name": "androidx/compose/ui/node/NodeKindKt.class", "size": 23681, "crc": -1680614873}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$ApproachMeasureBlock.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$ApproachMeasureBlock.class", "size": 1190, "crc": -413386942}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "size": 4146, "crc": -48890292}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$EmptyPlaceable.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$EmptyPlaceable.class", "size": 1981, "crc": 2138996994}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicMinMax.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicMinMax.class", "size": 1636, "crc": 770511426}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicWidthHeight.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicWidthHeight.class", "size": 1676, "crc": -1537662144}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$MeasureBlock.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$MeasureBlock.class", "size": 1150, "crc": -222492797}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics.class", "size": 7838, "crc": 2035146138}, {"key": "androidx/compose/ui/node/Nodes.class", "name": "androidx/compose/ui/node/Nodes.class", "size": 7058, "crc": 1196996999}, {"key": "androidx/compose/ui/node/ObserverModifierNode.class", "name": "androidx/compose/ui/node/ObserverModifierNode.class", "size": 564, "crc": -645212100}, {"key": "androidx/compose/ui/node/ObserverModifierNodeKt.class", "name": "androidx/compose/ui/node/ObserverModifierNodeKt.class", "size": 3118, "crc": 1725094991}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion$OnObserveReadsChanged$1.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion$OnObserveReadsChanged$1.class", "size": 1753, "crc": 33612592}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion.class", "size": 1404, "crc": -421370250}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope.class", "size": 2507, "crc": -1168690941}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher$Companion$DepthComparator.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher$Companion$DepthComparator.class", "size": 1897, "crc": 1720981631}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher$Companion.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher$Companion.class", "size": 1014, "crc": 956215712}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher.class", "size": 5763, "crc": -558669444}, {"key": "androidx/compose/ui/node/OwnedLayer.class", "name": "androidx/compose/ui/node/OwnedLayer.class", "size": 2681, "crc": -1265499521}, {"key": "androidx/compose/ui/node/Owner$Companion.class", "name": "androidx/compose/ui/node/Owner$Companion.class", "size": 995, "crc": -2331920}, {"key": "androidx/compose/ui/node/Owner$OnLayoutCompletedListener.class", "name": "androidx/compose/ui/node/Owner$OnLayoutCompletedListener.class", "size": 568, "crc": -1820269890}, {"key": "androidx/compose/ui/node/Owner.class", "name": "androidx/compose/ui/node/Owner.class", "size": 13863, "crc": 1620645593}, {"key": "androidx/compose/ui/node/OwnerScope.class", "name": "androidx/compose/ui/node/OwnerScope.class", "size": 453, "crc": -31207765}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$clearInvalidObservations$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$clearInvalidObservations$1.class", "size": 1811, "crc": -1347455720}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayout$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayout$1.class", "size": 1726, "crc": -1819826665}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifier$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifier$1.class", "size": 1742, "crc": 914974350}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifierInLookahead$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifierInLookahead$1.class", "size": 1773, "crc": -755091364}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookahead$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookahead$1.class", "size": 1741, "crc": -745719106}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookaheadMeasure$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookaheadMeasure$1.class", "size": 1761, "crc": 1774663177}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingMeasure$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingMeasure$1.class", "size": 1734, "crc": -1730291288}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingSemantics$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingSemantics$1.class", "size": 1662, "crc": -1198771291}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver.class", "size": 7674, "crc": -588571501}, {"key": "androidx/compose/ui/node/ParentDataModifierNode.class", "name": "androidx/compose/ui/node/ParentDataModifierNode.class", "size": 888, "crc": 2003468032}, {"key": "androidx/compose/ui/node/ParentDataModifierNodeKt.class", "name": "androidx/compose/ui/node/ParentDataModifierNodeKt.class", "size": 1022, "crc": -891709605}, {"key": "androidx/compose/ui/node/PlaceableResult.class", "name": "androidx/compose/ui/node/PlaceableResult.class", "size": 3491, "crc": 1434253709}, {"key": "androidx/compose/ui/node/PointerInputModifierNode.class", "name": "androidx/compose/ui/node/PointerInputModifierNode.class", "size": 1600, "crc": 121622442}, {"key": "androidx/compose/ui/node/PointerInputModifierNodeKt.class", "name": "androidx/compose/ui/node/PointerInputModifierNodeKt.class", "size": 2524, "crc": -516202760}, {"key": "androidx/compose/ui/node/Ref.class", "name": "androidx/compose/ui/node/Ref.class", "size": 1236, "crc": -1319823590}, {"key": "androidx/compose/ui/node/RootForTest.class", "name": "androidx/compose/ui/node/RootForTest.class", "size": 2031, "crc": -1997980126}, {"key": "androidx/compose/ui/node/SemanticsModifierNode.class", "name": "androidx/compose/ui/node/SemanticsModifierNode.class", "size": 1164, "crc": 139793851}, {"key": "androidx/compose/ui/node/SemanticsModifierNodeKt.class", "name": "androidx/compose/ui/node/SemanticsModifierNodeKt.class", "size": 3981, "crc": -928483652}, {"key": "androidx/compose/ui/node/Snake.class", "name": "androidx/compose/ui/node/Snake.class", "size": 4171, "crc": 1102380105}, {"key": "androidx/compose/ui/node/TailModifierNode.class", "name": "androidx/compose/ui/node/TailModifierNode.class", "size": 1597, "crc": -1056705033}, {"key": "androidx/compose/ui/node/TraversableNode$Companion$TraverseDescendantsAction.class", "name": "androidx/compose/ui/node/TraversableNode$Companion$TraverseDescendantsAction.class", "size": 1898, "crc": 1237535346}, {"key": "androidx/compose/ui/node/TraversableNode$Companion.class", "name": "androidx/compose/ui/node/TraversableNode$Companion.class", "size": 848, "crc": -2117448649}, {"key": "androidx/compose/ui/node/TraversableNode.class", "name": "androidx/compose/ui/node/TraversableNode.class", "size": 944, "crc": -1614062837}, {"key": "androidx/compose/ui/node/TraversableNodeKt.class", "name": "androidx/compose/ui/node/TraversableNodeKt.class", "size": 29739, "crc": -811745889}, {"key": "androidx/compose/ui/node/TreeSet.class", "name": "androidx/compose/ui/node/TreeSet.class", "size": 1255, "crc": -1891566805}, {"key": "androidx/compose/ui/node/UiApplier.class", "name": "androidx/compose/ui/node/UiApplier.class", "size": 2673, "crc": -299733674}, {"key": "androidx/compose/ui/node/ViewAdapter.class", "name": "androidx/compose/ui/node/ViewAdapter.class", "size": 1162, "crc": -172259039}, {"key": "androidx/compose/ui/node/ViewInterop_androidKt.class", "name": "androidx/compose/ui/node/ViewInterop_androidKt.class", "size": 4739, "crc": -1859768825}, {"key": "androidx/compose/ui/node/WeakReference_jvmKt.class", "name": "androidx/compose/ui/node/WeakReference_jvmKt.class", "size": 419, "crc": -1583993257}, {"key": "androidx/compose/ui/platform/AbstractComposeView$ensureCompositionCreated$1.class", "name": "androidx/compose/ui/platform/AbstractComposeView$ensureCompositionCreated$1.class", "size": 2497, "crc": -2101427393}, {"key": "androidx/compose/ui/platform/AbstractComposeView.class", "name": "androidx/compose/ui/platform/AbstractComposeView.class", "size": 14311, "crc": -1061037211}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$AbstractTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$AbstractTextSegmentIterator.class", "size": 2187, "crc": 2136661110}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator$Companion.class", "size": 2178, "crc": 1829862628}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator.class", "size": 3907, "crc": -772237587}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator$Companion.class", "size": 2005, "crc": 461484225}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator.class", "size": 4717, "crc": -498240285}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator$Companion.class", "size": 2005, "crc": 1724108405}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator.class", "size": 7104, "crc": 1930821221}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator$Companion.class", "size": 1889, "crc": 586625117}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator.class", "size": 3289, "crc": 2050218668}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$TextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$TextSegmentIterator.class", "size": 803, "crc": -1876265372}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator$Companion.class", "size": 2133, "crc": 1671981771}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator.class", "size": 4459, "crc": 261719025}, {"key": "androidx/compose/ui/platform/AccessibilityIterators.class", "name": "androidx/compose/ui/platform/AccessibilityIterators.class", "size": 1619, "crc": -1648440884}, {"key": "androidx/compose/ui/platform/AccessibilityManager$DefaultImpls.class", "name": "androidx/compose/ui/platform/AccessibilityManager$DefaultImpls.class", "size": 601, "crc": -907752833}, {"key": "androidx/compose/ui/platform/AccessibilityManager.class", "name": "androidx/compose/ui/platform/AccessibilityManager.class", "size": 1254, "crc": -1078085137}, {"key": "androidx/compose/ui/platform/AndroidAccessibilityManager$Companion.class", "name": "androidx/compose/ui/platform/AndroidAccessibilityManager$Companion.class", "size": 1031, "crc": -1710908287}, {"key": "androidx/compose/ui/platform/AndroidAccessibilityManager.class", "name": "androidx/compose/ui/platform/AndroidAccessibilityManager.class", "size": 3073, "crc": 490581161}, {"key": "androidx/compose/ui/platform/AndroidClipboardManager.class", "name": "androidx/compose/ui/platform/AndroidClipboardManager.class", "size": 5165, "crc": -217233946}, {"key": "androidx/compose/ui/platform/AndroidClipboardManager_androidKt.class", "name": "androidx/compose/ui/platform/AndroidClipboardManager_androidKt.class", "size": 8163, "crc": -9718995}, {"key": "androidx/compose/ui/platform/AndroidComposeView$Companion.class", "name": "androidx/compose/ui/platform/AndroidComposeView$Companion.class", "size": 2397, "crc": -204850331}, {"key": "androidx/compose/ui/platform/AndroidComposeView$ViewTreeOwners.class", "name": "androidx/compose/ui/platform/AndroidComposeView$ViewTreeOwners.class", "size": 1668, "crc": -225923094}, {"key": "androidx/compose/ui/platform/AndroidComposeView$_inputModeManager$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$_inputModeManager$1.class", "size": 2082, "crc": 188574954}, {"key": "androidx/compose/ui/platform/AndroidComposeView$addAndroidView$1$onInitializeAccessibilityNodeInfo$parentId$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$addAndroidView$1$onInitializeAccessibilityNodeInfo$parentId$1.class", "size": 3111, "crc": 396729690}, {"key": "androidx/compose/ui/platform/AndroidComposeView$addAndroidView$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$addAndroidView$1.class", "size": 4792, "crc": -714720631}, {"key": "androidx/compose/ui/platform/AndroidComposeView$configurationChangeObserver$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$configurationChangeObserver$1.class", "size": 1562, "crc": -1985242620}, {"key": "androidx/compose/ui/platform/AndroidComposeView$contentCaptureManager$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$contentCaptureManager$1.class", "size": 1788, "crc": 805024094}, {"key": "androidx/compose/ui/platform/AndroidComposeView$dispatchKeyEvent$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$dispatchKeyEvent$1.class", "size": 1620, "crc": -1174635558}, {"key": "androidx/compose/ui/platform/AndroidComposeView$dragAndDropModifierOnDragListener$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$dragAndDropModifierOnDragListener$1.class", "size": 2671, "crc": -404854590}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$1.class", "size": 1709, "crc": -2076779791}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$2.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$2.class", "size": 2212, "crc": 1724404792}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$3.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$3.class", "size": 1715, "crc": 581025322}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$4.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$4.class", "size": 1381, "crc": 1325226927}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$5.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$5.class", "size": 1517, "crc": 2061048395}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$6.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusOwner$6.class", "size": 1525, "crc": -715422635}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusSearch$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusSearch$1.class", "size": 1651, "crc": -1840349420}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$1.class", "size": 2059, "crc": -1096084481}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$focusWasMovedOrCancelled$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$focusWasMovedOrCancelled$1.class", "size": 2109, "crc": 62595970}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1.class", "size": 5912, "crc": 1195817598}, {"key": "androidx/compose/ui/platform/AndroidComposeView$pointerIconService$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$pointerIconService$1.class", "size": 2401, "crc": 888467460}, {"key": "androidx/compose/ui/platform/AndroidComposeView$removeAndroidView$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$removeAndroidView$1.class", "size": 2166, "crc": 1145379293}, {"key": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$1.class", "size": 1834, "crc": 2055156559}, {"key": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventOnLayout$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventOnLayout$1.class", "size": 2157, "crc": -2091976498}, {"key": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventRunnable$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventRunnable$1.class", "size": 1969, "crc": 648318997}, {"key": "androidx/compose/ui/platform/AndroidComposeView$rotaryInputModifier$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$rotaryInputModifier$1.class", "size": 1712, "crc": -2101879623}, {"key": "androidx/compose/ui/platform/AndroidComposeView$snapshotObserver$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$snapshotObserver$1.class", "size": 2562, "crc": 1362771143}, {"key": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$1.class", "size": 1798, "crc": 1001465403}, {"key": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$2.class", "name": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$2.class", "size": 2147, "crc": 877683503}, {"key": "androidx/compose/ui/platform/AndroidComposeView$viewTreeOwners$2.class", "name": "androidx/compose/ui/platform/AndroidComposeView$viewTreeOwners$2.class", "size": 1744, "crc": -1573120822}, {"key": "androidx/compose/ui/platform/AndroidComposeView.class", "name": "androidx/compose/ui/platform/AndroidComposeView.class", "size": 114983, "crc": -1081330106}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$1.class", "size": 3771, "crc": 878228885}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api24Impl.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api24Impl.class", "size": 3066, "crc": 1642734257}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api29Impl.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api29Impl.class", "size": 3734, "crc": **********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Companion.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Companion.class", "size": 1576, "crc": -**********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$ComposeAccessibilityNodeProvider.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$ComposeAccessibilityNodeProvider.class", "size": 3785, "crc": 780986204}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$LtrBoundsComparator.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$LtrBoundsComparator.class", "size": 2229, "crc": **********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$PendingTextTraversedEvent.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$PendingTextTraversedEvent.class", "size": 2149, "crc": 377535660}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$RtlBoundsComparator.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$RtlBoundsComparator.class", "size": 2229, "crc": -66610567}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$TopBottomBoundsComparator.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$TopBottomBoundsComparator.class", "size": 2407, "crc": 1047359679}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$WhenMappings.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$WhenMappings.class", "size": 969, "crc": 1834341937}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$boundsUpdatesEventLoop$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$boundsUpdatesEventLoop$1.class", "size": 2163, "crc": -577640195}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$geometryDepthFirstSearch$isTraversalGroup$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$geometryDepthFirstSearch$isTraversalGroup$1.class", "size": 1576, "crc": -410719484}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$onSendAccessibilityEvent$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$onSendAccessibilityEvent$1.class", "size": 2290, "crc": 1801281751}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeeded$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeeded$1.class", "size": 5646, "crc": -266553706}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeededLambda$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeededLambda$1.class", "size": 2069, "crc": 1474041734}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$semanticComparator$$inlined$thenBy$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$semanticComparator$$inlined$thenBy$1.class", "size": 2813, "crc": 1246501209}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$semanticComparator$$inlined$thenBy$2.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$semanticComparator$$inlined$thenBy$2.class", "size": 2856, "crc": -1210545524}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$1.class", "size": 2111, "crc": -816952573}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$semanticsNode$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$semanticsNode$1.class", "size": 3404, "crc": 1802566626}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sortByGeometryGroupings$2$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sortByGeometryGroupings$2$1.class", "size": 1544, "crc": 1728495976}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sortByGeometryGroupings$2$2.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sortByGeometryGroupings$2$2.class", "size": 1544, "crc": 1315215673}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sortByGeometryGroupings$2.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sortByGeometryGroupings$2.class", "size": 3142, "crc": 556278151}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat.class", "size": 125566, "crc": 512554386}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$excludeLineAndPageGranularities$ancestor$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$excludeLineAndPageGranularities$ancestor$1.class", "size": 2543, "crc": 2108163847}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt.class", "size": 7322, "crc": -527979173}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAssistHelperMethodsO.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAssistHelperMethodsO.class", "size": 1436, "crc": -934328247}, {"key": "androidx/compose/ui/platform/AndroidComposeViewForceDarkModeQ.class", "name": "androidx/compose/ui/platform/AndroidComposeViewForceDarkModeQ.class", "size": 1177, "crc": -444361775}, {"key": "androidx/compose/ui/platform/AndroidComposeViewStartDragAndDropN.class", "name": "androidx/compose/ui/platform/AndroidComposeViewStartDragAndDropN.class", "size": 1905, "crc": 1958182389}, {"key": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallback.class", "name": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallback.class", "size": 2152, "crc": 1224984997}, {"key": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallbackS.class", "name": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallbackS.class", "size": 1709, "crc": 1162420087}, {"key": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsN.class", "name": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsN.class", "size": 2125, "crc": -379671606}, {"key": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsO.class", "name": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsO.class", "size": 1347, "crc": -1453179751}, {"key": "androidx/compose/ui/platform/AndroidComposeView_androidKt$platformTextInputServiceInterceptor$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView_androidKt$platformTextInputServiceInterceptor$1.class", "size": 1709, "crc": 24558255}, {"key": "androidx/compose/ui/platform/AndroidComposeView_androidKt.class", "name": "androidx/compose/ui/platform/AndroidComposeView_androidKt.class", "size": 8198, "crc": 1898020540}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalConfiguration$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalConfiguration$1.class", "size": 1479, "crc": 1377408869}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalContext$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalContext$1.class", "size": 1431, "crc": -398064975}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalImageVectorCache$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalImageVectorCache$1.class", "size": 1509, "crc": 947490142}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalResourceIdCache$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalResourceIdCache$1.class", "size": 1503, "crc": 947999771}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalSavedStateRegistryOwner$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalSavedStateRegistryOwner$1.class", "size": 1539, "crc": -816985872}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalView$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalView$1.class", "size": 1404, "crc": -552701772}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$1$1.class", "size": 2124, "crc": -248713633}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1$invoke$$inlined$onDispose$1.class", "size": 2474, "crc": -124030998}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1.class", "size": 3320, "crc": 1808745180}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$3.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$3.class", "size": 3521, "crc": -883083933}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$4.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$4.class", "size": 2331, "crc": 1159766129}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1$invoke$$inlined$onDispose$1.class", "size": 2733, "crc": 1004225992}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1.class", "size": 3790, "crc": 465125419}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$callbacks$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$callbacks$1$1.class", "size": 2104, "crc": 1928612742}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1$invoke$$inlined$onDispose$1.class", "size": 2725, "crc": 2087973824}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1.class", "size": 3743, "crc": -257248304}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$callbacks$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$callbacks$1$1.class", "size": 1775, "crc": 700415668}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt.class", "size": 24480, "crc": -826228560}, {"key": "androidx/compose/ui/platform/AndroidFontResourceLoader.class", "name": "androidx/compose/ui/platform/AndroidFontResourceLoader.class", "size": 3064, "crc": -1967791496}, {"key": "androidx/compose/ui/platform/AndroidFontResourceLoaderHelper.class", "name": "androidx/compose/ui/platform/AndroidFontResourceLoaderHelper.class", "size": 1412, "crc": -1119124761}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$1.class", "size": 1944, "crc": 1551638341}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2$1.class", "size": 1811, "crc": -2016117056}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2.class", "size": 2460, "crc": 1790145285}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3$1$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3$1$1.class", "size": 2181, "crc": 664083625}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3.class", "size": 6607, "crc": -938469037}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession.class", "size": 5910, "crc": 1110462379}, {"key": "androidx/compose/ui/platform/AndroidTextToolbar$textActionModeCallback$1.class", "name": "androidx/compose/ui/platform/AndroidTextToolbar$textActionModeCallback$1.class", "size": 1385, "crc": 314216361}, {"key": "androidx/compose/ui/platform/AndroidTextToolbar.class", "name": "androidx/compose/ui/platform/AndroidTextToolbar.class", "size": 4920, "crc": 1935181816}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2$dispatcher$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2$dispatcher$1.class", "size": 3434, "crc": 865773386}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2.class", "size": 2601, "crc": 1323355106}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$currentThread$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$currentThread$1.class", "size": 2778, "crc": 1658416586}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion.class", "size": 2030, "crc": 1987775292}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$dispatchCallback$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$dispatchCallback$1.class", "size": 3566, "crc": -379500692}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher.class", "size": 10031, "crc": -1532556861}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher_androidKt.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher_androidKt.class", "size": 660, "crc": -759023825}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$1.class", "size": 2009, "crc": 70733014}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$2.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$2.class", "size": 2014, "crc": -34076317}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$callback$1.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$callback$1.class", "size": 3128, "crc": -551436866}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock.class", "size": 7861, "crc": -1934274335}, {"key": "androidx/compose/ui/platform/AndroidUriHandler.class", "name": "androidx/compose/ui/platform/AndroidUriHandler.class", "size": 2078, "crc": 261767458}, {"key": "androidx/compose/ui/platform/AndroidViewConfiguration.class", "name": "androidx/compose/ui/platform/AndroidViewConfiguration.class", "size": 2862, "crc": -1810510819}, {"key": "androidx/compose/ui/platform/AndroidViewConfigurationApi34.class", "name": "androidx/compose/ui/platform/AndroidViewConfigurationApi34.class", "size": 1369, "crc": 1118367765}, {"key": "androidx/compose/ui/platform/AndroidViewsHandler.class", "name": "androidx/compose/ui/platform/AndroidViewsHandler.class", "size": 7704, "crc": -522709393}, {"key": "androidx/compose/ui/platform/Api28ClipboardManagerClipClear.class", "name": "androidx/compose/ui/platform/Api28ClipboardManagerClipClear.class", "size": 1222, "crc": -1986386329}, {"key": "androidx/compose/ui/platform/Api29Impl.class", "name": "androidx/compose/ui/platform/Api29Impl.class", "size": 1407, "crc": 1645006872}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindow.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindow.class", "size": 791, "crc": 2125338933}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindowApi21.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindowApi21.class", "size": 3323, "crc": 1587749569}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindowApi29.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindowApi29.class", "size": 2321, "crc": -70762343}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$1.class", "size": 1970, "crc": 1348516452}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$1.class", "size": 2287, "crc": 531000030}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$2.class", "size": 1848, "crc": -777845178}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$1.class", "size": 2107, "crc": 963159432}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$2.class", "size": 4670, "crc": 1339136384}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3.class", "size": 5240, "crc": 332923828}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1.class", "size": 4984, "crc": 1881452314}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2.class", "size": 5052, "crc": -253692578}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor.class", "size": 6086, "crc": -639904910}, {"key": "androidx/compose/ui/platform/ClipEntry.class", "name": "androidx/compose/ui/platform/ClipEntry.class", "size": 1620, "crc": -2006901246}, {"key": "androidx/compose/ui/platform/ClipMetadata.class", "name": "androidx/compose/ui/platform/ClipMetadata.class", "size": 1144, "crc": 1488410138}, {"key": "androidx/compose/ui/platform/ClipboardExtensions_androidKt.class", "name": "androidx/compose/ui/platform/ClipboardExtensions_androidKt.class", "size": 1363, "crc": 530207767}, {"key": "androidx/compose/ui/platform/ClipboardManager.class", "name": "androidx/compose/ui/platform/ClipboardManager.class", "size": 1972, "crc": 1026645831}, {"key": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt$lambda-1$1.class", "size": 2250, "crc": 1588025739}, {"key": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt.class", "name": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt.class", "size": 1529, "crc": 1661234054}, {"key": "androidx/compose/ui/platform/ComposeView$Content$1.class", "name": "androidx/compose/ui/platform/ComposeView$Content$1.class", "size": 1650, "crc": -579649184}, {"key": "androidx/compose/ui/platform/ComposeView.class", "name": "androidx/compose/ui/platform/ComposeView.class", "size": 5373, "crc": -177088336}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAccessibilityManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAccessibilityManager$1.class", "size": 1326, "crc": 665811131}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofill$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofill$1.class", "size": 1266, "crc": -213378309}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillTree$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillTree$1.class", "size": 1440, "crc": 1249014731}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboardManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboardManager$1.class", "size": 1464, "crc": -952437814}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalDensity$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalDensity$1.class", "size": 1398, "crc": -1219761494}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFocusManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFocusManager$1.class", "size": 1431, "crc": 1496057460}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontFamilyResolver$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontFamilyResolver$1.class", "size": 1602, "crc": -696052633}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontLoader$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontLoader$1.class", "size": 1578, "crc": 2033912234}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalGraphicsContext$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalGraphicsContext$1.class", "size": 1458, "crc": -853522322}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalHapticFeedback$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalHapticFeedback$1.class", "size": 1470, "crc": -1328743089}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalInputModeManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalInputModeManager$1.class", "size": 1451, "crc": 666352369}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalLayoutDirection$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalLayoutDirection$1.class", "size": 1446, "crc": -1097384411}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalPointerIconService$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalPointerIconService$1.class", "size": 1331, "crc": -1284106646}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalProvidableScrollCaptureInProgress$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalProvidableScrollCaptureInProgress$1.class", "size": 1311, "crc": 1286357979}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalSoftwareKeyboardController$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalSoftwareKeyboardController$1.class", "size": 1356, "crc": -1467826786}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextInputService$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextInputService$1.class", "size": 1312, "crc": -1692781599}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextToolbar$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextToolbar$1.class", "size": 1434, "crc": 728066819}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalUriHandler$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalUriHandler$1.class", "size": 1428, "crc": 1134438203}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalViewConfiguration$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalViewConfiguration$1.class", "size": 1470, "crc": -1696536216}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalWindowInfo$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalWindowInfo$1.class", "size": 1428, "crc": -1598723239}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$ProvideCommonCompositionLocals$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$ProvideCommonCompositionLocals$1.class", "size": 2419, "crc": 101625391}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt.class", "size": 20925, "crc": 1178031353}, {"key": "androidx/compose/ui/platform/DebugUtilsKt.class", "name": "androidx/compose/ui/platform/DebugUtilsKt.class", "size": 859, "crc": 1971247344}, {"key": "androidx/compose/ui/platform/DecodeHelper.class", "name": "androidx/compose/ui/platform/DecodeHelper.class", "size": 9228, "crc": 2057475106}, {"key": "androidx/compose/ui/platform/DelegatingSoftwareKeyboardController.class", "name": "androidx/compose/ui/platform/DelegatingSoftwareKeyboardController.class", "size": 1659, "crc": 1356071194}, {"key": "androidx/compose/ui/platform/DeviceRenderNode.class", "name": "androidx/compose/ui/platform/DeviceRenderNode.class", "size": 4837, "crc": 1419682466}, {"key": "androidx/compose/ui/platform/DeviceRenderNodeData.class", "name": "androidx/compose/ui/platform/DeviceRenderNodeData.class", "size": 14258, "crc": -1868877921}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry.class", "size": 3076, "crc": -558887445}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$1.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$1.class", "size": 1769, "crc": 701981073}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$saveableStateRegistry$1.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$saveableStateRegistry$1.class", "size": 1843, "crc": -673092386}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt.class", "size": 9924, "crc": -1737907671}, {"key": "androidx/compose/ui/platform/DragAndDropModifierOnDragListener$modifier$1.class", "name": "androidx/compose/ui/platform/DragAndDropModifierOnDragListener$modifier$1.class", "size": 3013, "crc": -932631367}, {"key": "androidx/compose/ui/platform/DragAndDropModifierOnDragListener$rootDragAndDropNode$1.class", "name": "androidx/compose/ui/platform/DragAndDropModifierOnDragListener$rootDragAndDropNode$1.class", "size": 1799, "crc": -584464617}, {"key": "androidx/compose/ui/platform/DragAndDropModifierOnDragListener.class", "name": "androidx/compose/ui/platform/DragAndDropModifierOnDragListener.class", "size": 7597, "crc": 1795281099}, {"key": "androidx/compose/ui/platform/DrawChildContainer.class", "name": "androidx/compose/ui/platform/DrawChildContainer.class", "size": 3517, "crc": 1309867235}, {"key": "androidx/compose/ui/platform/EncodeHelper.class", "name": "androidx/compose/ui/platform/EncodeHelper.class", "size": 8739, "crc": 908063697}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$1.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$1.class", "size": 6502, "crc": 1829293782}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$2.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$2.class", "size": 1846, "crc": -547763294}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager.class", "size": 3116, "crc": 1660211509}, {"key": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer$recordLambda$1.class", "name": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer$recordLambda$1.class", "size": 3908, "crc": 1279156302}, {"key": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer.class", "name": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer.class", "size": 22290, "crc": 1197181995}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy$DefaultImpls.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy$DefaultImpls.class", "size": 3497, "crc": -200984025}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy$Key.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy$Key.class", "size": 1090, "crc": -1162648435}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy.class", "size": 2180, "crc": -924871329}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameNanos$2.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameNanos$2.class", "size": 3371, "crc": 1145521156}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt.class", "size": 2216, "crc": 1681198920}, {"key": "androidx/compose/ui/platform/InputMethodSession$createInputConnection$1$1.class", "name": "androidx/compose/ui/platform/InputMethodSession$createInputConnection$1$1.class", "size": 4181, "crc": 1079314202}, {"key": "androidx/compose/ui/platform/InputMethodSession.class", "name": "androidx/compose/ui/platform/InputMethodSession.class", "size": 6812, "crc": -517344853}, {"key": "androidx/compose/ui/platform/InspectableModifier$End.class", "name": "androidx/compose/ui/platform/InspectableModifier$End.class", "size": 949, "crc": -1458772114}, {"key": "androidx/compose/ui/platform/InspectableModifier.class", "name": "androidx/compose/ui/platform/InspectableModifier.class", "size": 1886, "crc": 1208809569}, {"key": "androidx/compose/ui/platform/InspectableValue$DefaultImpls.class", "name": "androidx/compose/ui/platform/InspectableValue$DefaultImpls.class", "size": 1520, "crc": -730462381}, {"key": "androidx/compose/ui/platform/InspectableValue.class", "name": "androidx/compose/ui/platform/InspectableValue.class", "size": 1984, "crc": 215297725}, {"key": "androidx/compose/ui/platform/InspectableValueKt$NoInspectorInfo$1.class", "name": "androidx/compose/ui/platform/InspectableValueKt$NoInspectorInfo$1.class", "size": 1490, "crc": -1303249504}, {"key": "androidx/compose/ui/platform/InspectableValueKt$debugInspectorInfo$1.class", "name": "androidx/compose/ui/platform/InspectableValueKt$debugInspectorInfo$1.class", "size": 2302, "crc": 606596727}, {"key": "androidx/compose/ui/platform/InspectableValueKt.class", "name": "androidx/compose/ui/platform/InspectableValueKt.class", "size": 4645, "crc": 1332396130}, {"key": "androidx/compose/ui/platform/InspectionModeKt$LocalInspectionMode$1.class", "name": "androidx/compose/ui/platform/InspectionModeKt$LocalInspectionMode$1.class", "size": 1261, "crc": 1291628832}, {"key": "androidx/compose/ui/platform/InspectionModeKt.class", "name": "androidx/compose/ui/platform/InspectionModeKt.class", "size": 1370, "crc": 571719070}, {"key": "androidx/compose/ui/platform/InspectorInfo.class", "name": "androidx/compose/ui/platform/InspectorInfo.class", "size": 1887, "crc": 737593396}, {"key": "androidx/compose/ui/platform/InspectorValueInfo.class", "name": "androidx/compose/ui/platform/InspectorValueInfo.class", "size": 2877, "crc": -1495266003}, {"key": "androidx/compose/ui/platform/InvertMatrixKt.class", "name": "androidx/compose/ui/platform/InvertMatrixKt.class", "size": 5982, "crc": 1601104116}, {"key": "androidx/compose/ui/platform/JvmActuals_jvmKt.class", "name": "androidx/compose/ui/platform/JvmActuals_jvmKt.class", "size": 3073, "crc": -1914190889}, {"key": "androidx/compose/ui/platform/LayerMatrixCache.class", "name": "androidx/compose/ui/platform/LayerMatrixCache.class", "size": 3989, "crc": 30993839}, {"key": "androidx/compose/ui/platform/MotionDurationScaleImpl.class", "name": "androidx/compose/ui/platform/MotionDurationScaleImpl.class", "size": 4704, "crc": -651258497}, {"key": "androidx/compose/ui/platform/MotionEventVerifierApi29.class", "name": "androidx/compose/ui/platform/MotionEventVerifierApi29.class", "size": 1394, "crc": 1772272973}, {"key": "androidx/compose/ui/platform/MutableSpanStyle.class", "name": "androidx/compose/ui/platform/MutableSpanStyle.class", "size": 10211, "crc": 9540935}, {"key": "androidx/compose/ui/platform/NestedScrollInteropConnection.class", "name": "androidx/compose/ui/platform/NestedScrollInteropConnection.class", "size": 5076, "crc": 524460462}, {"key": "androidx/compose/ui/platform/NestedScrollInteropConnectionKt.class", "name": "androidx/compose/ui/platform/NestedScrollInteropConnectionKt.class", "size": 7153, "crc": 1543953449}, {"key": "androidx/compose/ui/platform/OutlineResolver.class", "name": "androidx/compose/ui/platform/OutlineResolver.class", "size": 11483, "crc": -1063973582}, {"key": "androidx/compose/ui/platform/PlatformTextInputInterceptor.class", "name": "androidx/compose/ui/platform/PlatformTextInputInterceptor.class", "size": 1315, "crc": -558154898}, {"key": "androidx/compose/ui/platform/PlatformTextInputMethodRequest.class", "name": "androidx/compose/ui/platform/PlatformTextInputMethodRequest.class", "size": 853, "crc": 628635466}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNode.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNode.class", "size": 534, "crc": -1046690985}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$InterceptPlatformTextInput$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$InterceptPlatformTextInput$1.class", "size": 2350, "crc": 284153148}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$LocalChainedPlatformTextInputInterceptor$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$LocalChainedPlatformTextInputInterceptor$1.class", "size": 1449, "crc": 1322416248}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$establishTextInputSession$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$establishTextInputSession$1.class", "size": 1729, "crc": -299210287}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$interceptedTextInputSession$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$interceptedTextInputSession$1.class", "size": 1826, "crc": -1926409017}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt.class", "size": 11716, "crc": -2114469060}, {"key": "androidx/compose/ui/platform/PlatformTextInputSession.class", "name": "androidx/compose/ui/platform/PlatformTextInputSession.class", "size": 1152, "crc": -463398323}, {"key": "androidx/compose/ui/platform/PlatformTextInputSessionScope.class", "name": "androidx/compose/ui/platform/PlatformTextInputSessionScope.class", "size": 650, "crc": -798755008}, {"key": "androidx/compose/ui/platform/RenderNodeApi23$Companion.class", "name": "androidx/compose/ui/platform/RenderNodeApi23$Companion.class", "size": 1316, "crc": -1924903481}, {"key": "androidx/compose/ui/platform/RenderNodeApi23.class", "name": "androidx/compose/ui/platform/RenderNodeApi23.class", "size": 16766, "crc": -271306080}, {"key": "androidx/compose/ui/platform/RenderNodeApi29.class", "name": "androidx/compose/ui/platform/RenderNodeApi29.class", "size": 14205, "crc": 551373690}, {"key": "androidx/compose/ui/platform/RenderNodeApi29VerificationHelper.class", "name": "androidx/compose/ui/platform/RenderNodeApi29VerificationHelper.class", "size": 1618, "crc": -649212576}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$Companion$getMatrix$1.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$Companion$getMatrix$1.class", "size": 1764, "crc": 582756731}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$Companion.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$Companion.class", "size": 1029, "crc": 1416035557}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$UniqueDrawingIdApi29.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$UniqueDrawingIdApi29.class", "size": 1277, "crc": 556310514}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$updateDisplayList$1$1.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$updateDisplayList$1$1.class", "size": 1954, "crc": -813092799}, {"key": "androidx/compose/ui/platform/RenderNodeLayer.class", "name": "androidx/compose/ui/platform/RenderNodeLayer.class", "size": 17897, "crc": -1471780975}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper23.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper23.class", "size": 1160, "crc": -113651021}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper24.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper24.class", "size": 1156, "crc": 1379974444}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper28.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper28.class", "size": 1727, "crc": 1413947572}, {"key": "androidx/compose/ui/platform/ScrollObservationScope.class", "name": "androidx/compose/ui/platform/ScrollObservationScope.class", "size": 3738, "crc": -780546741}, {"key": "androidx/compose/ui/platform/SemanticsNodeCopy.class", "name": "androidx/compose/ui/platform/SemanticsNodeCopy.class", "size": 3500, "crc": 2095274730}, {"key": "androidx/compose/ui/platform/SemanticsNodeWithAdjustedBounds.class", "name": "androidx/compose/ui/platform/SemanticsNodeWithAdjustedBounds.class", "size": 1504, "crc": 1344267164}, {"key": "androidx/compose/ui/platform/SemanticsUtils_androidKt.class", "name": "androidx/compose/ui/platform/SemanticsUtils_androidKt.class", "size": 12787, "crc": 1179307506}, {"key": "androidx/compose/ui/platform/ShapeContainingUtilKt.class", "name": "androidx/compose/ui/platform/ShapeContainingUtilKt.class", "size": 6189, "crc": 1970489905}, {"key": "androidx/compose/ui/platform/SoftwareKeyboardController.class", "name": "androidx/compose/ui/platform/SoftwareKeyboardController.class", "size": 587, "crc": -2113057686}, {"key": "androidx/compose/ui/platform/TestTagElement.class", "name": "androidx/compose/ui/platform/TestTagElement.class", "size": 2929, "crc": -1653059914}, {"key": "androidx/compose/ui/platform/TestTagKt.class", "name": "androidx/compose/ui/platform/TestTagKt.class", "size": 1036, "crc": 1202554715}, {"key": "androidx/compose/ui/platform/TestTagNode.class", "name": "androidx/compose/ui/platform/TestTagNode.class", "size": 1697, "crc": -1898045097}, {"key": "androidx/compose/ui/platform/TextToolbar$DefaultImpls.class", "name": "androidx/compose/ui/platform/TextToolbar$DefaultImpls.class", "size": 700, "crc": -755482537}, {"key": "androidx/compose/ui/platform/TextToolbar.class", "name": "androidx/compose/ui/platform/TextToolbar.class", "size": 2249, "crc": -1531235153}, {"key": "androidx/compose/ui/platform/TextToolbarHelperMethods.class", "name": "androidx/compose/ui/platform/TextToolbarHelperMethods.class", "size": 1902, "crc": -1427051230}, {"key": "androidx/compose/ui/platform/TextToolbarStatus.class", "name": "androidx/compose/ui/platform/TextToolbarStatus.class", "size": 1433, "crc": -1125201881}, {"key": "androidx/compose/ui/platform/UriHandler.class", "name": "androidx/compose/ui/platform/UriHandler.class", "size": 570, "crc": 795069060}, {"key": "androidx/compose/ui/platform/ValueElement.class", "name": "androidx/compose/ui/platform/ValueElement.class", "size": 2924, "crc": -1643453569}, {"key": "androidx/compose/ui/platform/ValueElementSequence.class", "name": "androidx/compose/ui/platform/ValueElementSequence.class", "size": 1957, "crc": -1380405747}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$Companion.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$Companion.class", "size": 1382, "crc": 966321139}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$1.class", "size": 2149, "crc": -2010445630}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$listener$1.class", "size": 1819, "crc": -1053445612}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow.class", "size": 2487, "crc": -710361915}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$1.class", "size": 2677, "crc": 1906592319}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$listener$1.class", "size": 2054, "crc": -1426013849}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool.class", "size": 3581, "crc": -1872543323}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnLifecycleDestroyed.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnLifecycleDestroyed.class", "size": 2269, "crc": 894179183}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$1.class", "size": 2205, "crc": 2057646278}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$2.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$2.class", "size": 1852, "crc": -51493251}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$listener$1.class", "size": 4884, "crc": -1249614118}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed.class", "size": 5280, "crc": 857007828}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy.class", "size": 1796, "crc": -575060410}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt$installForLifecycle$2.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt$installForLifecycle$2.class", "size": 1665, "crc": -823789757}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt.class", "size": 3421, "crc": -318408385}, {"key": "androidx/compose/ui/platform/ViewConfiguration$DefaultImpls.class", "name": "androidx/compose/ui/platform/ViewConfiguration$DefaultImpls.class", "size": 1386, "crc": **********}, {"key": "androidx/compose/ui/platform/ViewConfiguration.class", "name": "androidx/compose/ui/platform/ViewConfiguration.class", "size": 3094, "crc": 287274088}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion$OutlineProvider$1.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion$OutlineProvider$1.class", "size": 1672, "crc": 519276579}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion$getMatrix$1.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion$getMatrix$1.class", "size": 1705, "crc": **********}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion.class", "size": 4643, "crc": **********}, {"key": "androidx/compose/ui/platform/ViewLayer$UniqueDrawingIdApi29.class", "name": "androidx/compose/ui/platform/ViewLayer$UniqueDrawingIdApi29.class", "size": 1253, "crc": -363330035}, {"key": "androidx/compose/ui/platform/ViewLayer.class", "name": "androidx/compose/ui/platform/ViewLayer.class", "size": 20746, "crc": -**********}, {"key": "androidx/compose/ui/platform/ViewLayerContainer.class", "name": "androidx/compose/ui/platform/ViewLayerContainer.class", "size": 1305, "crc": -**********}, {"key": "androidx/compose/ui/platform/ViewLayerVerificationHelper28.class", "name": "androidx/compose/ui/platform/ViewLayerVerificationHelper28.class", "size": 1370, "crc": 1774778032}, {"key": "androidx/compose/ui/platform/ViewLayerVerificationHelper31.class", "name": "androidx/compose/ui/platform/ViewLayerVerificationHelper31.class", "size": 1567, "crc": -2020071327}, {"key": "androidx/compose/ui/platform/ViewRootForInspector$DefaultImpls.class", "name": "androidx/compose/ui/platform/ViewRootForInspector$DefaultImpls.class", "size": 1190, "crc": -2108695100}, {"key": "androidx/compose/ui/platform/ViewRootForInspector.class", "name": "androidx/compose/ui/platform/ViewRootForInspector.class", "size": 1507, "crc": -167653344}, {"key": "androidx/compose/ui/platform/ViewRootForTest$Companion.class", "name": "androidx/compose/ui/platform/ViewRootForTest$Companion.class", "size": 1874, "crc": -1481106533}, {"key": "androidx/compose/ui/platform/ViewRootForTest.class", "name": "androidx/compose/ui/platform/ViewRootForTest.class", "size": 1254, "crc": -1259411556}, {"key": "androidx/compose/ui/platform/WeakCache.class", "name": "androidx/compose/ui/platform/WeakCache.class", "size": 3794, "crc": -359830857}, {"key": "androidx/compose/ui/platform/WindowInfo.class", "name": "androidx/compose/ui/platform/WindowInfo.class", "size": 1503, "crc": 159198770}, {"key": "androidx/compose/ui/platform/WindowInfoImpl$Companion.class", "name": "androidx/compose/ui/platform/WindowInfoImpl$Companion.class", "size": 1419, "crc": 157327159}, {"key": "androidx/compose/ui/platform/WindowInfoImpl.class", "name": "androidx/compose/ui/platform/WindowInfoImpl.class", "size": 3207, "crc": 2050990560}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$1.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$1.class", "size": 1479, "crc": -905761720}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$2.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$2.class", "size": 2290, "crc": -1053815274}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1.class", "size": 4449, "crc": -2049644708}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$2.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$2.class", "size": 1904, "crc": -385191268}, {"key": "androidx/compose/ui/platform/WindowInfoKt.class", "name": "androidx/compose/ui/platform/WindowInfoKt.class", "size": 5479, "crc": -2083151703}, {"key": "androidx/compose/ui/platform/WindowRecomposerFactory$Companion.class", "name": "androidx/compose/ui/platform/WindowRecomposerFactory$Companion.class", "size": 2047, "crc": -487455901}, {"key": "androidx/compose/ui/platform/WindowRecomposerFactory.class", "name": "androidx/compose/ui/platform/WindowRecomposerFactory.class", "size": 1129, "crc": 1746721991}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$1.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$1.class", "size": 1855, "crc": -1879871751}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$unsetJob$1.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$unsetJob$1.class", "size": 4473, "crc": 1482844969}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy.class", "size": 6311, "crc": -306440746}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$1.class", "size": 1919, "crc": 1080003768}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$WhenMappings.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$WhenMappings.class", "size": 1218, "crc": 986421968}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1$1.class", "size": 2360, "crc": -2143947506}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1.class", "size": 4517, "crc": -1660624509}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1.class", "size": 7377, "crc": 1582190777}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2.class", "size": 4100, "crc": 363144268}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$1.class", "size": 6260, "crc": -676656987}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$contentObserver$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$contentObserver$1.class", "size": 1800, "crc": -1735499047}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt.class", "size": 15074, "crc": -455829628}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$1$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$1$1.class", "size": 3835, "crc": 1747123593}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$2$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$2$1.class", "size": 3836, "crc": 1650888021}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$3.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$3.class", "size": 3174, "crc": -228289860}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1.class", "size": 7436, "crc": 2122734819}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1.class", "size": 3744, "crc": -693140408}, {"key": "androidx/compose/ui/platform/WrappedComposition.class", "name": "androidx/compose/ui/platform/WrappedComposition.class", "size": 5610, "crc": 1871780933}, {"key": "androidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods.class", "name": "androidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods.class", "size": 1540, "crc": -1782249185}, {"key": "androidx/compose/ui/platform/Wrapper_androidKt.class", "name": "androidx/compose/ui/platform/Wrapper_androidKt.class", "size": 6014, "crc": -225606928}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$setCollectionItemInfo$itemInfo$1.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$setCollectionItemInfo$itemInfo$1.class", "size": 1499, "crc": 699104835}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$toAccessibilityCollectionItemInfo$1.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$toAccessibilityCollectionItemInfo$1.class", "size": 1783, "crc": -2087211446}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt.class", "size": 10755, "crc": -945673882}, {"key": "androidx/compose/ui/platform/actionmodecallback/FloatingTextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/FloatingTextActionModeCallback.class", "size": 3191, "crc": 214203082}, {"key": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption$WhenMappings.class", "name": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption$WhenMappings.class", "size": 960, "crc": 1601253195}, {"key": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption.class", "name": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption.class", "size": 2497, "crc": 1279240719}, {"key": "androidx/compose/ui/platform/actionmodecallback/PrimaryTextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/PrimaryTextActionModeCallback.class", "size": 2372, "crc": 1457197703}, {"key": "androidx/compose/ui/platform/actionmodecallback/TextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/TextActionModeCallback.class", "size": 9101, "crc": -66716005}, {"key": "androidx/compose/ui/res/ColorResourceHelper.class", "name": "androidx/compose/ui/res/ColorResourceHelper.class", "size": 1647, "crc": -2119782582}, {"key": "androidx/compose/ui/res/ColorResources_androidKt.class", "name": "androidx/compose/ui/res/ColorResources_androidKt.class", "size": 3453, "crc": 1559633801}, {"key": "androidx/compose/ui/res/FontResources_androidKt.class", "name": "androidx/compose/ui/res/FontResources_androidKt.class", "size": 5396, "crc": -1024705577}, {"key": "androidx/compose/ui/res/ImageResources_androidKt.class", "name": "androidx/compose/ui/res/ImageResources_androidKt.class", "size": 5949, "crc": -1080457477}, {"key": "androidx/compose/ui/res/ImageVectorCache$ImageVectorEntry.class", "name": "androidx/compose/ui/res/ImageVectorCache$ImageVectorEntry.class", "size": 3268, "crc": -1658089309}, {"key": "androidx/compose/ui/res/ImageVectorCache$Key.class", "name": "androidx/compose/ui/res/ImageVectorCache$Key.class", "size": 3183, "crc": 1846795196}, {"key": "androidx/compose/ui/res/ImageVectorCache.class", "name": "androidx/compose/ui/res/ImageVectorCache.class", "size": 3247, "crc": -651633952}, {"key": "androidx/compose/ui/res/PainterResources_androidKt.class", "name": "androidx/compose/ui/res/PainterResources_androidKt.class", "size": 10527, "crc": -1368656022}, {"key": "androidx/compose/ui/res/PrimitiveResources_androidKt.class", "name": "androidx/compose/ui/res/PrimitiveResources_androidKt.class", "size": 5569, "crc": -87992113}, {"key": "androidx/compose/ui/res/ResourceIdCache.class", "name": "androidx/compose/ui/res/ResourceIdCache.class", "size": 2305, "crc": -1600172552}, {"key": "androidx/compose/ui/res/ResourceResolutionException.class", "name": "androidx/compose/ui/res/ResourceResolutionException.class", "size": 1085, "crc": -1075050849}, {"key": "androidx/compose/ui/res/Resources_androidKt.class", "name": "androidx/compose/ui/res/Resources_androidKt.class", "size": 2999, "crc": -432240761}, {"key": "androidx/compose/ui/res/StringResources_androidKt.class", "name": "androidx/compose/ui/res/StringResources_androidKt.class", "size": 4387, "crc": 777404384}, {"key": "androidx/compose/ui/res/VectorResources_androidKt.class", "name": "androidx/compose/ui/res/VectorResources_androidKt.class", "size": 9104, "crc": 389646479}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$ScrollCaptureSessionListener.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$ScrollCaptureSessionListener.class", "size": 743, "crc": -836140279}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureEnd$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureEnd$1.class", "size": 4445, "crc": 637605173}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$1.class", "size": 4908, "crc": -920175555}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$2.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$2.class", "size": 2456, "crc": 1855641548}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$3.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$3.class", "size": 1541, "crc": -306059359}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$scrollTracker$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$scrollTracker$1.class", "size": 6977, "crc": 1277923461}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback.class", "size": 13689, "crc": -1441864820}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt$launchWithCancellationSignal$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt$launchWithCancellationSignal$1.class", "size": 1826, "crc": 185143131}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt.class", "size": 3361, "crc": 461662582}, {"key": "androidx/compose/ui/scrollcapture/DisableAnimationMotionDurationScale.class", "name": "androidx/compose/ui/scrollcapture/DisableAnimationMotionDurationScale.class", "size": 3129, "crc": -1709834529}, {"key": "androidx/compose/ui/scrollcapture/RelativeScroller$scrollBy$1.class", "name": "androidx/compose/ui/scrollcapture/RelativeScroller$scrollBy$1.class", "size": 1923, "crc": 843031185}, {"key": "androidx/compose/ui/scrollcapture/RelativeScroller.class", "name": "androidx/compose/ui/scrollcapture/RelativeScroller.class", "size": 5545, "crc": 569365656}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$1.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$1.class", "size": 1859, "crc": -1017755435}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$2.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$2.class", "size": 2031, "crc": -224924973}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$3.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$3.class", "size": 2150, "crc": 1988835742}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture.class", "size": 9199, "crc": -835821188}, {"key": "androidx/compose/ui/scrollcapture/ScrollCaptureCandidate.class", "name": "androidx/compose/ui/scrollcapture/ScrollCaptureCandidate.class", "size": 2479, "crc": 1683229411}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture_androidKt.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture_androidKt.class", "size": 10286, "crc": -1381237043}, {"key": "androidx/compose/ui/semantics/AccessibilityAction.class", "name": "androidx/compose/ui/semantics/AccessibilityAction.class", "size": 2580, "crc": 602087700}, {"key": "androidx/compose/ui/semantics/AppendedSemanticsElement.class", "name": "androidx/compose/ui/semantics/AppendedSemanticsElement.class", "size": 6781, "crc": -1043335779}, {"key": "androidx/compose/ui/semantics/ClearAndSetSemanticsElement.class", "name": "androidx/compose/ui/semantics/ClearAndSetSemanticsElement.class", "size": 6050, "crc": 1511098102}, {"key": "androidx/compose/ui/semantics/CollectionInfo.class", "name": "androidx/compose/ui/semantics/CollectionInfo.class", "size": 1056, "crc": -512185898}, {"key": "androidx/compose/ui/semantics/CollectionItemInfo.class", "name": "androidx/compose/ui/semantics/CollectionItemInfo.class", "size": 1397, "crc": 392562659}, {"key": "androidx/compose/ui/semantics/CoreSemanticsModifierNode.class", "name": "androidx/compose/ui/semantics/CoreSemanticsModifierNode.class", "size": 3429, "crc": 1027620632}, {"key": "androidx/compose/ui/semantics/CustomAccessibilityAction.class", "name": "androidx/compose/ui/semantics/CustomAccessibilityAction.class", "size": 2627, "crc": 352604277}, {"key": "androidx/compose/ui/semantics/EmptySemanticsElement.class", "name": "androidx/compose/ui/semantics/EmptySemanticsElement.class", "size": 2661, "crc": -1009295433}, {"key": "androidx/compose/ui/semantics/EmptySemanticsModifier.class", "name": "androidx/compose/ui/semantics/EmptySemanticsModifier.class", "size": 1326, "crc": 1444524544}, {"key": "androidx/compose/ui/semantics/LiveRegionMode$Companion.class", "name": "androidx/compose/ui/semantics/LiveRegionMode$Companion.class", "size": 1260, "crc": -919571056}, {"key": "androidx/compose/ui/semantics/LiveRegionMode.class", "name": "androidx/compose/ui/semantics/LiveRegionMode.class", "size": 2655, "crc": 259017914}, {"key": "androidx/compose/ui/semantics/ProgressBarRangeInfo$Companion.class", "name": "androidx/compose/ui/semantics/ProgressBarRangeInfo$Companion.class", "size": 1228, "crc": -857402547}, {"key": "androidx/compose/ui/semantics/ProgressBarRangeInfo.class", "name": "androidx/compose/ui/semantics/ProgressBarRangeInfo.class", "size": 4491, "crc": -1775263002}, {"key": "androidx/compose/ui/semantics/Role$Companion.class", "name": "androidx/compose/ui/semantics/Role$Companion.class", "size": 2028, "crc": -1132476800}, {"key": "androidx/compose/ui/semantics/Role.class", "name": "androidx/compose/ui/semantics/Role.class", "size": 3243, "crc": -2115627649}, {"key": "androidx/compose/ui/semantics/ScrollAxisRange.class", "name": "androidx/compose/ui/semantics/ScrollAxisRange.class", "size": 2720, "crc": 1850047393}, {"key": "androidx/compose/ui/semantics/SemanticsActions.class", "name": "androidx/compose/ui/semantics/SemanticsActions.class", "size": 17778, "crc": -1678231112}, {"key": "androidx/compose/ui/semantics/SemanticsConfiguration.class", "name": "androidx/compose/ui/semantics/SemanticsConfiguration.class", "size": 10101, "crc": 383780294}, {"key": "androidx/compose/ui/semantics/SemanticsConfigurationKt$getOrNull$1.class", "name": "androidx/compose/ui/semantics/SemanticsConfigurationKt$getOrNull$1.class", "size": 1260, "crc": -871788683}, {"key": "androidx/compose/ui/semantics/SemanticsConfigurationKt.class", "name": "androidx/compose/ui/semantics/SemanticsConfigurationKt.class", "size": 1575, "crc": -357622397}, {"key": "androidx/compose/ui/semantics/SemanticsModifier$DefaultImpls.class", "name": "androidx/compose/ui/semantics/SemanticsModifier$DefaultImpls.class", "size": 2992, "crc": -995066704}, {"key": "androidx/compose/ui/semantics/SemanticsModifier.class", "name": "androidx/compose/ui/semantics/SemanticsModifier.class", "size": 2564, "crc": 825027429}, {"key": "androidx/compose/ui/semantics/SemanticsModifierKt.class", "name": "androidx/compose/ui/semantics/SemanticsModifierKt.class", "size": 5994, "crc": -2061316012}, {"key": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$1.class", "name": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$1.class", "size": 1874, "crc": 2009597051}, {"key": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$2.class", "name": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$2.class", "size": 1799, "crc": -764628701}, {"key": "androidx/compose/ui/semantics/SemanticsNode$fakeSemanticsNode$fakeNode$1.class", "name": "androidx/compose/ui/semantics/SemanticsNode$fakeSemanticsNode$fakeNode$1.class", "size": 1889, "crc": -632505081}, {"key": "androidx/compose/ui/semantics/SemanticsNode$isUnmergedLeafNode$1.class", "name": "androidx/compose/ui/semantics/SemanticsNode$isUnmergedLeafNode$1.class", "size": 1880, "crc": 2909571}, {"key": "androidx/compose/ui/semantics/SemanticsNode$parent$1.class", "name": "androidx/compose/ui/semantics/SemanticsNode$parent$1.class", "size": 1863, "crc": -318989259}, {"key": "androidx/compose/ui/semantics/SemanticsNode$parent$2.class", "name": "androidx/compose/ui/semantics/SemanticsNode$parent$2.class", "size": 2580, "crc": -675985374}, {"key": "androidx/compose/ui/semantics/SemanticsNode.class", "name": "androidx/compose/ui/semantics/SemanticsNode.class", "size": 20554, "crc": -1110529472}, {"key": "androidx/compose/ui/semantics/SemanticsNodeKt.class", "name": "androidx/compose/ui/semantics/SemanticsNodeKt.class", "size": 13493, "crc": 1296517987}, {"key": "androidx/compose/ui/semantics/SemanticsOwner.class", "name": "androidx/compose/ui/semantics/SemanticsOwner.class", "size": 2239, "crc": -1092875069}, {"key": "androidx/compose/ui/semantics/SemanticsOwnerKt.class", "name": "androidx/compose/ui/semantics/SemanticsOwnerKt.class", "size": 5055, "crc": 1843431352}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentDataType$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentDataType$1.class", "size": 1763, "crc": -451017534}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentDescription$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentDescription$1.class", "size": 2650, "crc": -2136133315}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentType$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentType$1.class", "size": 1756, "crc": -2020882089}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$InvisibleToUser$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$InvisibleToUser$1.class", "size": 1540, "crc": 418910403}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$IsDialog$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$IsDialog$1.class", "size": 1722, "crc": 1250834706}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$IsPopup$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$IsPopup$1.class", "size": 1718, "crc": -646433758}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$PaneTitle$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$PaneTitle$1.class", "size": 1693, "crc": 1920279632}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$Role$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$Role$1.class", "size": 1671, "crc": 1940273559}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$TestTag$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$TestTag$1.class", "size": 1559, "crc": -259278590}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$Text$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$Text$1.class", "size": 2769, "crc": 1790141858}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$TraversalIndex$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$TraversalIndex$1.class", "size": 1566, "crc": -629670867}, {"key": "androidx/compose/ui/semantics/SemanticsProperties.class", "name": "androidx/compose/ui/semantics/SemanticsProperties.class", "size": 15810, "crc": 563131953}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid$TestTagsAsResourceId$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid$TestTagsAsResourceId$1.class", "size": 1601, "crc": 1463802423}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid.class", "size": 1946, "crc": -617587946}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt$ActionPropertyKey$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt$ActionPropertyKey$1.class", "size": 3040, "crc": -1402450025}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt$getScrollViewportLength$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt$getScrollViewportLength$1.class", "size": 2154, "crc": -1101988686}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt.class", "size": 42660, "crc": -654546844}, {"key": "androidx/compose/ui/semantics/SemanticsProperties_androidKt.class", "name": "androidx/compose/ui/semantics/SemanticsProperties_androidKt.class", "size": 2884, "crc": 1537721779}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyKey$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyKey$1.class", "size": 1437, "crc": 1177913813}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyKey.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyKey.class", "size": 4706, "crc": 1872756430}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyReceiver.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyReceiver.class", "size": 876, "crc": 1579719522}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt.class", "size": 848, "crc": -1904782773}, {"key": "androidx/compose/ui/state/ToggleableState.class", "name": "androidx/compose/ui/state/ToggleableState.class", "size": 1462, "crc": 139965320}, {"key": "androidx/compose/ui/state/ToggleableStateKt.class", "name": "androidx/compose/ui/state/ToggleableStateKt.class", "size": 786, "crc": -209744094}, {"key": "androidx/compose/ui/text/TextMeasurerHelperKt.class", "name": "androidx/compose/ui/text/TextMeasurerHelperKt.class", "size": 4990, "crc": 1213444066}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoApi33Helper.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoApi33Helper.class", "size": 2141, "crc": 1157093111}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoApi34Helper.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoApi34Helper.class", "size": 2260, "crc": 165115089}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoBuilder_androidKt.class", "size": 8216, "crc": 137108346}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController$invalidate$1$1.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController$invalidate$1$1.class", "size": 1550, "crc": -1898306430}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController$textFieldToRootTransform$1.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController$textFieldToRootTransform$1.class", "size": 1672, "crc": -440967101}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController.class", "size": 7579, "crc": 1889177760}, {"key": "androidx/compose/ui/text/input/InputEventCallback2.class", "name": "androidx/compose/ui/text/input/InputEventCallback2.class", "size": 1772, "crc": 1186210306}, {"key": "androidx/compose/ui/text/input/InputMethodManager.class", "name": "androidx/compose/ui/text/input/InputMethodManager.class", "size": 1475, "crc": -1085087074}, {"key": "androidx/compose/ui/text/input/InputMethodManagerImpl$imm$2.class", "name": "androidx/compose/ui/text/input/InputMethodManagerImpl$imm$2.class", "size": 1957, "crc": 1263707003}, {"key": "androidx/compose/ui/text/input/InputMethodManagerImpl.class", "name": "androidx/compose/ui/text/input/InputMethodManagerImpl.class", "size": 4119, "crc": 1400008389}, {"key": "androidx/compose/ui/text/input/InputState_androidKt.class", "name": "androidx/compose/ui/text/input/InputState_androidKt.class", "size": 1697, "crc": -1649162880}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapper.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapper.class", "size": 690, "crc": 246083798}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi21.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi21.class", "size": 7718, "crc": -848153490}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi24.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi24.class", "size": 2295, "crc": 2122841756}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi25.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi25.class", "size": 2043, "crc": 1422683702}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi34.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi34.class", "size": 2594, "crc": 811817700}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapper_androidKt.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapper_androidKt.class", "size": 2049, "crc": -1540051593}, {"key": "androidx/compose/ui/text/input/RecordingInputConnection.class", "name": "androidx/compose/ui/text/input/RecordingInputConnection.class", "size": 19638, "crc": 696471522}, {"key": "androidx/compose/ui/text/input/RecordingInputConnection_androidKt.class", "name": "androidx/compose/ui/text/input/RecordingInputConnection_androidKt.class", "size": 698, "crc": -276411326}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$TextInputCommand.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$TextInputCommand.class", "size": 1847, "crc": 2053311324}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$WhenMappings.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$WhenMappings.class", "size": 1089, "crc": -678878721}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$baseInputConnection$2.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$baseInputConnection$2.class", "size": 1720, "crc": -134150976}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$createInputConnection$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$createInputConnection$1.class", "size": 4133, "crc": -777352459}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$onEditCommand$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$onEditCommand$1.class", "size": 1762, "crc": -410088351}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$onImeActionPerformed$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$onImeActionPerformed$1.class", "size": 1617, "crc": -1656587469}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$1.class", "size": 1605, "crc": -220736892}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$2.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$2.class", "size": 1446, "crc": -125019648}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid.class", "size": 20316, "crc": -2020622517}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid_androidKt.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid_androidKt.class", "size": 6657, "crc": 55483911}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion$OnCommitAffectingUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion$OnCommitAffectingUpdate$1.class", "size": 2408, "crc": -1126207321}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion.class", "size": 1023, "crc": 222443190}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$1.class", "size": 1932, "crc": 1821374510}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$2.class", "size": 1777, "crc": -1012105088}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$3.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$3.class", "size": 2214, "crc": 1234116149}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$4.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$4.class", "size": 1941, "crc": 1191418636}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$1.class", "size": 1775, "crc": 1223997789}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$2.class", "size": 2190, "crc": 1389583629}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5.class", "size": 5404, "crc": -195984573}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$1.class", "size": 1778, "crc": -568900445}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$2.class", "size": 4322, "crc": 1150784961}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$3.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$3.class", "size": 2386, "crc": 446066745}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedFling$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedFling$1.class", "size": 4222, "crc": 1730025274}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedPreFling$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedPreFling$1.class", "size": 3862, "crc": -2078640821}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$release$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$release$1.class", "size": 1314, "crc": -1698987573}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$reset$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$reset$1.class", "size": 1310, "crc": 1139071591}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$runInvalidate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$runInvalidate$1.class", "size": 1577, "crc": -248703912}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$runUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$runUpdate$1.class", "size": 2282, "crc": 2038893283}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$update$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$update$1.class", "size": 1312, "crc": 2002896985}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder.class", "size": 27248, "crc": -1264973016}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt$NoOpScrollConnection$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt$NoOpScrollConnection$1.class", "size": 867, "crc": 316390634}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt.class", "size": 4281, "crc": 262868903}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$1.class", "size": 2354, "crc": -1877828233}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$1.class", "size": 2381, "crc": 366882282}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$2.class", "size": 2382, "crc": -1254265334}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$3.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$3.class", "size": 2383, "crc": 1541368668}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$1.class", "size": 2382, "crc": 900824023}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$2.class", "size": 2383, "crc": 1812469030}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$4.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$4.class", "size": 2700, "crc": 408747091}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$NoOpUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$NoOpUpdate$1.class", "size": 1401, "crc": -1314767268}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$createAndroidViewNodeFactory$1$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$createAndroidViewNodeFactory$1$1.class", "size": 2931, "crc": -748997517}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$1.class", "size": 2324, "crc": -644276868}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$2.class", "size": 2343, "crc": -1807391390}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$3.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$3.class", "size": 2355, "crc": -182007925}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$4.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$4.class", "size": 2418, "crc": 1903145058}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5$WhenMappings.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5$WhenMappings.class", "size": 896, "crc": -596010201}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5.class", "size": 2692, "crc": 782526663}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt.class", "size": 20053, "crc": -1198087145}, {"key": "androidx/compose/ui/viewinterop/FocusGroupNode_androidKt.class", "name": "androidx/compose/ui/viewinterop/FocusGroupNode_androidKt.class", "size": 4724, "crc": 381003142}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesElement.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesElement.class", "size": 2651, "crc": -2057321339}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$applyFocusProperties$1.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$applyFocusProperties$1.class", "size": 1674, "crc": -783051804}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$applyFocusProperties$2.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$applyFocusProperties$2.class", "size": 1671, "crc": -1669322109}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode.class", "size": 16243, "crc": -81021383}, {"key": "androidx/compose/ui/viewinterop/FocusTargetPropertiesElement.class", "name": "androidx/compose/ui/viewinterop/FocusTargetPropertiesElement.class", "size": 2659, "crc": -1799777789}, {"key": "androidx/compose/ui/viewinterop/FocusTargetPropertiesNode.class", "name": "androidx/compose/ui/viewinterop/FocusTargetPropertiesNode.class", "size": 1607, "crc": -**********}, {"key": "androidx/compose/ui/viewinterop/InteropViewFactoryHolder_androidKt.class", "name": "androidx/compose/ui/viewinterop/InteropViewFactoryHolder_androidKt.class", "size": 462, "crc": **********}, {"key": "androidx/compose/ui/viewinterop/InteropView_androidKt.class", "name": "androidx/compose/ui/viewinterop/InteropView_androidKt.class", "size": 388, "crc": -**********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$registerSaveStateProvider$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$registerSaveStateProvider$1.class", "size": 1842, "crc": **********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$releaseBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$releaseBlock$1.class", "size": 1804, "crc": **********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$resetBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$resetBlock$1.class", "size": 1738, "crc": -710672027}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$updateBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$updateBlock$1.class", "size": 1742, "crc": **********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder.class", "size": 9549, "crc": -**********}, {"key": "androidx/compose/ui/window/AlignmentOffsetPositionProvider.class", "name": "androidx/compose/ui/window/AlignmentOffsetPositionProvider.class", "size": 3319, "crc": -**********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1$invoke$$inlined$onDispose$1.class", "size": 2203, "crc": -525593096}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1.class", "size": 2993, "crc": **********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$2$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$2$1.class", "size": 2232, "crc": **********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$3.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$3.class", "size": 2510, "crc": -**********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1$1.class", "size": 1724, "crc": 865207248}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1$2.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1$2.class", "size": 2986, "crc": 657316300}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1.class", "size": 3937, "crc": 1060829341}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialogId$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialogId$1.class", "size": 1377, "crc": 1973931567}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1$1.class", "size": 3370, "crc": 1729375247}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1.class", "size": 5713, "crc": -40119139}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$2.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$2.class", "size": 2231, "crc": -1814731104}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt.class", "size": 16315, "crc": -1689307526}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$LocalPopupTestTag$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$LocalPopupTestTag$1.class", "size": 1219, "crc": 1857044021}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$1.class", "size": 2741, "crc": -987975499}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1$invoke$$inlined$onDispose$1.class", "size": 2188, "crc": 765314300}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1.class", "size": 4027, "crc": -899782619}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$3$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$3$1.class", "size": 2395, "crc": -1518296190}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1$invoke$$inlined$onDispose$1.class", "size": 1932, "crc": 1874735204}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1.class", "size": 3285, "crc": -1410605694}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1$1.class", "size": 1330, "crc": -2005888403}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1.class", "size": 4339, "crc": 756776021}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$7$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$7$1.class", "size": 2095, "crc": -435617637}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1$1.class", "size": 1739, "crc": -930357146}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1.class", "size": 2597, "crc": -2127696537}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$9.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$9.class", "size": 2765, "crc": 1362592}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupId$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupId$1.class", "size": 1417, "crc": -965178787}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$1.class", "size": 1731, "crc": 1131150048}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$2$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$2$1.class", "size": 1807, "crc": -485665528}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$3.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$3.class", "size": 2990, "crc": -1650538109}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1.class", "size": 10939, "crc": 1717170138}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$PopupTestTag$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$PopupTestTag$1.class", "size": 2094, "crc": -379696733}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1.class", "size": 2204, "crc": 961486029}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$2.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$2.class", "size": 2415, "crc": -962921008}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$3.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$3.class", "size": 2817, "crc": -1018548435}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1.class", "size": 5044, "crc": 279458204}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt.class", "size": 29255, "crc": -1309472628}, {"key": "androidx/compose/ui/window/Api33Impl.class", "name": "androidx/compose/ui/window/Api33Impl.class", "size": 2954, "crc": -1741243589}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt$lambda-1$1.class", "size": 2284, "crc": -2035977842}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt.class", "size": 1551, "crc": -881804076}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt$lambda-1$1.class", "size": 2277, "crc": 2088833952}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt.class", "size": 1546, "crc": 1913545319}, {"key": "androidx/compose/ui/window/DialogLayout$Content$4.class", "name": "androidx/compose/ui/window/DialogLayout$Content$4.class", "size": 1647, "crc": -215991640}, {"key": "androidx/compose/ui/window/DialogLayout.class", "name": "androidx/compose/ui/window/DialogLayout.class", "size": 8859, "crc": 482102822}, {"key": "androidx/compose/ui/window/DialogProperties.class", "name": "androidx/compose/ui/window/DialogProperties.class", "size": 3892, "crc": -941218636}, {"key": "androidx/compose/ui/window/DialogWindowProvider.class", "name": "androidx/compose/ui/window/DialogWindowProvider.class", "size": 616, "crc": -**********}, {"key": "androidx/compose/ui/window/DialogWrapper$1$2.class", "name": "androidx/compose/ui/window/DialogWrapper$1$2.class", "size": 1444, "crc": -15418741}, {"key": "androidx/compose/ui/window/DialogWrapper$2.class", "name": "androidx/compose/ui/window/DialogWrapper$2.class", "size": 2165, "crc": -716297918}, {"key": "androidx/compose/ui/window/DialogWrapper$WhenMappings.class", "name": "androidx/compose/ui/window/DialogWrapper$WhenMappings.class", "size": 814, "crc": -**********}, {"key": "androidx/compose/ui/window/DialogWrapper.class", "name": "androidx/compose/ui/window/DialogWrapper.class", "size": 11959, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupLayout$2.class", "name": "androidx/compose/ui/window/PopupLayout$2.class", "size": 1504, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupLayout$Companion$onCommitAffectingPopupPosition$1.class", "name": "androidx/compose/ui/window/PopupLayout$Companion$onCommitAffectingPopupPosition$1.class", "size": 1576, "crc": 1069305166}, {"key": "androidx/compose/ui/window/PopupLayout$Companion.class", "name": "androidx/compose/ui/window/PopupLayout$Companion.class", "size": 981, "crc": 1985992164}, {"key": "androidx/compose/ui/window/PopupLayout$Content$4.class", "name": "androidx/compose/ui/window/PopupLayout$Content$4.class", "size": 1641, "crc": 317477506}, {"key": "androidx/compose/ui/window/PopupLayout$WhenMappings.class", "name": "androidx/compose/ui/window/PopupLayout$WhenMappings.class", "size": 809, "crc": 561060120}, {"key": "androidx/compose/ui/window/PopupLayout$canCalculatePosition$2.class", "name": "androidx/compose/ui/window/PopupLayout$canCalculatePosition$2.class", "size": 2624, "crc": -698797134}, {"key": "androidx/compose/ui/window/PopupLayout$snapshotStateObserver$1.class", "name": "androidx/compose/ui/window/PopupLayout$snapshotStateObserver$1.class", "size": 2719, "crc": 1015056453}, {"key": "androidx/compose/ui/window/PopupLayout$updatePosition$1.class", "name": "androidx/compose/ui/window/PopupLayout$updatePosition$1.class", "size": 2043, "crc": 1846630180}, {"key": "androidx/compose/ui/window/PopupLayout.class", "name": "androidx/compose/ui/window/PopupLayout.class", "size": 27800, "crc": -157572509}, {"key": "androidx/compose/ui/window/PopupLayoutHelper.class", "name": "androidx/compose/ui/window/PopupLayoutHelper.class", "size": 1395, "crc": -838476768}, {"key": "androidx/compose/ui/window/PopupLayoutHelperImpl.class", "name": "androidx/compose/ui/window/PopupLayoutHelperImpl.class", "size": 1969, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupLayoutHelperImpl29.class", "name": "androidx/compose/ui/window/PopupLayoutHelperImpl29.class", "size": 1402, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupPositionProvider.class", "name": "androidx/compose/ui/window/PopupPositionProvider.class", "size": 1069, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupProperties.class", "name": "androidx/compose/ui/window/PopupProperties.class", "size": 5354, "crc": -517714188}, {"key": "androidx/compose/ui/window/SecureFlagPolicy.class", "name": "androidx/compose/ui/window/SecureFlagPolicy.class", "size": 1489, "crc": **********}, {"key": "androidx/compose/ui/window/SecureFlagPolicy_androidKt$WhenMappings.class", "name": "androidx/compose/ui/window/SecureFlagPolicy_androidKt$WhenMappings.class", "size": 910, "crc": -653480444}, {"key": "androidx/compose/ui/window/SecureFlagPolicy_androidKt.class", "name": "androidx/compose/ui/window/SecureFlagPolicy_androidKt.class", "size": 1170, "crc": 411305491}, {"key": "androidx/inspection/compose/ui/ProguardDetection.class", "name": "androidx/inspection/compose/ui/ProguardDetection.class", "size": 545, "crc": -**********}, {"key": "androidx/compose/ui/platform/coreshims/AutofillIdCompat.class", "name": "androidx/compose/ui/platform/coreshims/AutofillIdCompat.class", "size": 1331, "crc": 102787729}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api23Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api23Impl.class", "size": 905, "crc": 1056502564}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api29Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api29Impl.class", "size": 2735, "crc": 1617845360}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api34Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api34Impl.class", "size": 1253, "crc": 582910621}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat.class", "size": 5734, "crc": -1357640860}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api26Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api26Impl.class", "size": 855, "crc": 276451393}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api29Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api29Impl.class", "size": 900, "crc": 1509913206}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api30Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api30Impl.class", "size": 833, "crc": 375207945}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims.class", "size": 2856, "crc": 1331267618}, {"key": "androidx/compose/ui/platform/coreshims/ViewStructureCompat$Api23Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewStructureCompat$Api23Impl.class", "size": 2280, "crc": 1038561795}, {"key": "androidx/compose/ui/platform/coreshims/ViewStructureCompat.class", "name": "androidx/compose/ui/platform/coreshims/ViewStructureCompat.class", "size": 3335, "crc": -1880427645}, {"key": "META-INF/androidx.compose.ui_ui.version", "name": "META-INF/androidx.compose.ui_ui.version", "size": 6, "crc": 501114004}, {"key": "META-INF/ui_release.kotlin_module", "name": "META-INF/ui_release.kotlin_module", "size": 4831, "crc": -779080245}]