package com.arijit.budgettracker.utils

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.arijit.budgettracker.R
import com.arijit.budgettracker.db.SavingsGoal

class SavingsGoalSelectionDialog(
    private val context: Context,
    private val goals: List<SavingsGoal>,
    private val onGoalSelected: (SavingsGoal?) -> Unit
) {
    
    fun show() {
        if (goals.isEmpty()) {
            // No goals available, just proceed without specific goal
            onGoalSelected(null)
            return
        }
        
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_goal_selection, null)
        val recyclerView = dialogView.findViewById<RecyclerView>(R.id.goals_recycler)
        val titleText = dialogView.findViewById<TextView>(R.id.dialog_title)
        
        titleText.text = "Choose Savings Goal"
        
        var dialog: AlertDialog? = null

        val adapter = GoalSelectionAdapter(goals) { selectedGoal ->
            onGoalSelected(selectedGoal)
            dialog?.dismiss()
        }

        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = adapter

        dialog = AlertDialog.Builder(context)
            .setView(dialogView)
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            .setNeutralButton("No Specific Goal") { dialog, _ ->
                onGoalSelected(null)
                dialog.dismiss()
            }
            .create()

        dialog.show()
    }
}
