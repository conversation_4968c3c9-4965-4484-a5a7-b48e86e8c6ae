<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/savings_goal_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="15dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/theme_surface"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Goal Title -->
        <TextView
            android:id="@+id/goal_title_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/montserrat_bold"
            android:text="Vacation Fund"
            android:textColor="@color/theme_text_secondary"
            android:textSize="18sp"
            android:layout_marginBottom="8dp" />

        <!-- Current and Target Amount Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/current_amount_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/montserrat_bold"
                android:text="₹15,000"
                android:textColor="@color/medium_green"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:text=" / "
                android:textColor="@color/theme_text_secondary"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/target_amount_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:text="₹50,000"
                android:textColor="@color/theme_text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/savings_progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginBottom="8dp"
            android:max="100"
            android:progress="30"
            android:progressTint="@color/medium_green" />

        <!-- Progress Percentage and Target Date Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/target_date_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/montserrat_regular"
                android:text="Target: Dec 31, 2024"
                android:textColor="@color/theme_text_secondary"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/savings_percentage_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_bold"
                android:text="30%"
                android:textColor="@color/medium_green"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
