<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Light theme -->
    <style name="Base.Theme.BudgetTracker" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Light theme colors -->
        <item name="colorPrimary">@color/light_green</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/medium_green</item>
        <item name="colorOnPrimaryContainer">@color/white</item>
        <item name="colorSecondary">@color/dark_green</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/background</item>
        <item name="colorOnSecondaryContainer">@color/white</item>
        <item name="colorTertiary">@color/medium_green</item>
        <item name="colorOnTertiary">@color/white</item>
        <item name="colorTertiaryContainer">@color/light_green</item>
        <item name="colorOnTertiaryContainer">@color/dark_green</item>
        <item name="colorError">@color/red</item>
        <item name="colorOnError">@color/white</item>
        <item name="colorErrorContainer">@color/red</item>
        <item name="colorOnErrorContainer">@color/white</item>
        <item name="colorOutline">@color/medium_green</item>
        <item name="colorOutlineVariant">@color/light_green</item>
        <item name="colorSurface">@color/background</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="colorSurfaceVariant">@color/dark_green</item>
        <item name="colorOnSurfaceVariant">@color/white</item>
        <item name="colorSurfaceContainer">@color/medium_green</item>
        <item name="colorSurfaceContainerHigh">@color/dark_green</item>
        <item name="colorSurfaceContainerHighest">@color/background</item>
        <item name="android:colorBackground">@color/background</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
        <item name="android:windowBackground">@color/background</item>
    </style>

    <style name="Theme.BudgetTracker" parent="Base.Theme.BudgetTracker" />
</resources>