package com.arijit.budgettracker

import android.app.DatePickerDialog
import android.os.Bundle
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.arijit.budgettracker.db.SavingsGoal
import com.arijit.budgettracker.models.BudgetViewModel
import com.arijit.budgettracker.utils.Vibration
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import android.content.Context
import androidx.appcompat.app.AppCompatDelegate

class AddGoalActivity : AppCompatActivity() {

    private lateinit var viewModel: BudgetViewModel
    private lateinit var backButton: ImageView
    private lateinit var saveButton: TextView
    private lateinit var goalTitleEdit: EditText
    private lateinit var targetAmountEdit: EditText
    private lateinit var targetDateText: TextView
    
    private var selectedTargetDate: Long = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        // Apply saved theme before calling super.onCreate()
        val sharedPrefs = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        val isDarkMode = sharedPrefs.getBoolean("dark_mode", false)
        if (isDarkMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        }

        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_add_goal)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initViews()
        setupClickListeners()
        
        viewModel = ViewModelProvider(this)[BudgetViewModel::class.java]
    }

    private fun initViews() {
        backButton = findViewById(R.id.back_btn)
        saveButton = findViewById(R.id.save_btn)
        goalTitleEdit = findViewById(R.id.goal_title_edit)
        targetAmountEdit = findViewById(R.id.target_amount_edit)
        targetDateText = findViewById(R.id.target_date_text)
    }

    private fun setupClickListeners() {
        backButton.setOnClickListener {
            Vibration.vibrate(this, 50)
            finish()
        }

        saveButton.setOnClickListener {
            Vibration.vibrate(this, 100)
            saveGoal()
        }

        targetDateText.setOnClickListener {
            Vibration.vibrate(this, 50)
            showDatePicker()
        }
    }

    private fun showDatePicker() {
        val calendar = Calendar.getInstance()
        val datePickerDialog = DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                calendar.set(year, month, dayOfMonth)
                selectedTargetDate = calendar.timeInMillis
                
                val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
                targetDateText.text = dateFormat.format(calendar.time)
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        )
        
        // Set minimum date to today
        datePickerDialog.datePicker.minDate = System.currentTimeMillis()
        datePickerDialog.show()
    }

    private fun saveGoal() {
        val title = goalTitleEdit.text.toString().trim()
        val targetAmountStr = targetAmountEdit.text.toString().trim()

        if (title.isEmpty()) {
            goalTitleEdit.error = "Please enter goal title"
            return
        }

        if (targetAmountStr.isEmpty()) {
            targetAmountEdit.error = "Please enter target amount"
            return
        }

        if (selectedTargetDate == 0L) {
            // Show error for date
            return
        }

        val targetAmount = targetAmountStr.toDoubleOrNull()
        if (targetAmount == null || targetAmount <= 0) {
            targetAmountEdit.error = "Please enter valid amount"
            return
        }

        lifecycleScope.launch {
            val savingsGoal = SavingsGoal(
                title = title,
                targetAmount = targetAmount,
                targetDate = selectedTargetDate
            )
            viewModel.insertSavingsGoal(savingsGoal)
            finish()
        }
    }
}
