# 💰 BudgetTracker

A modern **Expense Tracker Android app** built with **Kotlin**, designed to make money management simple and intuitive. It supports **multi-currency tracking**, lets users **categorize expenses**, and provides **visual insights** with interactive daily, weekly, and monthly charts.

---

## ✨ Features  
- 📊 Track **daily, weekly, and monthly expenses**  
- 💵 **Multi-currency support**  
- 🏷️ Categorize expenses for better organization  
- 📈 Interactive **charts & analytics** (bar + pie charts)  
- 🔐 Secure data handling  
- 🎨 Clean, minimal, and modern UI  

---

## 🚀 Tech Stack  
- **Language:** Kotlin  
- **Database:** Room 
- **UI:** XML 
- **Charts:** MPAndroidChart  

---

## 📷 Screenshots  

<p align="center">
  <img src="https://github.com/user-attachments/assets/21a6d495-b3a0-4e09-9dba-b2dc81ae3796" width="30%" />
  <img src="https://github.com/user-attachments/assets/8fa63795-aace-405a-8111-623bc4e38804" width="30%" />
  <img src="https://github.com/user-attachments/assets/0f1cb21c-665c-434d-a95c-73c7af4a7741" width="30%" />
</p>

---

## 📌 Upcoming ideas
- Export expense reports (PDF/Excel)
- Add cloud sync & backup
- Implement AI-based spending insights

## 🤝 Contributing
Contributions are welcome! Feel free to fork this repo and submit pull requests.

## 📄 License
This project is licensed under the MIT License.
