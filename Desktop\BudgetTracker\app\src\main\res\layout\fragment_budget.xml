<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbars="none"
    android:fillViewport="true"
    tools:context=".fragments.BudgetFragment">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- MONTHLY BUDGET SECTION -->
        <TextView
            android:id="@+id/monthly_budget_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="10dp"
            android:fontFamily="@font/montserrat_bold"
            android:text="Monthly Budget"
            android:textColor="@color/light_green"
            android:textSize="26sp" />

        <!-- SET BUDGET BUTTON -->
        <androidx.cardview.widget.CardView
            android:id="@+id/set_budget_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/monthly_budget_title"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="15dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/light_green"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/montserrat_bold"
                    android:text="Set Monthly Budgets"
                    android:textColor="@color/dark_green"
                    android:textSize="18sp" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_add"
                    app:tint="@color/dark_green" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- BUDGET PROGRESS SECTION -->
        <TextView
            android:id="@+id/budget_progress_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/set_budget_card"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="30dp"
            android:fontFamily="@font/montserrat_bold"
            android:text="Budget Progress"
            android:textColor="@color/light_green"
            android:textSize="22sp" />

        <!-- BUDGET PROGRESS RECYCLERVIEW -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/budget_progress_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/budget_progress_title"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="15dp"
            android:nestedScrollingEnabled="false" />

        <!-- SAVINGS GOALS SECTION -->
        <TextView
            android:id="@+id/savings_goals_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/budget_progress_rv"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="30dp"
            android:fontFamily="@font/montserrat_bold"
            android:text="Savings Goals"
            android:textColor="@color/light_green"
            android:textSize="22sp" />

        <!-- ADD GOAL BUTTON -->
        <androidx.cardview.widget.CardView
            android:id="@+id/add_goal_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/savings_goals_title"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="15dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/light_green"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/montserrat_bold"
                    android:text="Add Savings Goal"
                    android:textColor="@color/dark_green"
                    android:textSize="18sp" />

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_add"
                    app:tint="@color/dark_green" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- SAVINGS GOALS RECYCLERVIEW -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/savings_goals_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/add_goal_card"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="15dp"
            android:nestedScrollingEnabled="false" />

        <!-- BOTTOM SPACING -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:layout_below="@id/savings_goals_rv" />

    </RelativeLayout>

</ScrollView>
