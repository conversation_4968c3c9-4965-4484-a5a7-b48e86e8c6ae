-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:2:1-39:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b5538d2e8925004503c2603d09c856e\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c1e17eae3721ff358c86731c9840490\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29de5ec125a2d20c25e57930259370ec\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca8fe3c0abea6cb6fcafe94314e333b6\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\12f1630e93cea9fb6dc93cb1d9d1b1e0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a843704273e916f72829d3ad6c60aff0\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a806e04ac6a0c7130c07945d59d7d67\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5afd894b7ef3fcabb4292eac3f6b4a9\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\24f3b1a12e8091b80c07f679a9055e5f\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca069b81aae7f9145e18ceafffc36134\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\74211e39ebbd4933cb6e7197727b70c4\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e42930b94b257198714fe1affe4d6e15\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2f6a2a7e6923b1656d524cff8fc96b9\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f999ebb768e3ed3825c90024a28be6a6\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b261e901710f7c197c152ad6e2bf77a8\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\995c80fea8f44a626044b31ada671c08\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2fdda3cf75b79259a338795d156c6dd\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\98c4a339e587ec56442454db77daf607\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\65471f26bcd7bc579f6b93911b8a3f55\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b998037e39a80f60cd812ea9d31e7210\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be834f8e59ef0ad7665a00a59dbbb85\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\486ba7343e577de335f7776b30351bd9\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23abdce465d35a8007ecae11164b50f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\328887870c685989ca0947d484ef0aef\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\863923f39d7044ae6db3fd633409ba80\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe7ec8d21979a321f05334a63126ddd5\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ef392a8d9f9fe53b60422c58d8938a3\transformed\activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec7c3a0d685ca9f9b46a227fc236af1c\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\40299ef8d0f30d949093f49976625db6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\276a500444e49e362f086f375956db77\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a5c063d720e7c5445d3611c3440a882\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d7db48768896e526b918d011eba52b1\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18bbead6eb51c8084bc422305b982042\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c3000d299da206cd42e78a175b21c3b\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\051de53e7806c905f4312198682b9034\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f70d57ff2e0c8718394d455a5842fbc7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\51c7789b7ce8f7e520fa1c4de77b2784\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fe2e9f8814488ebcc04f055e472bb64\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bb35325efedde69ca4af8c2ec6f9423\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb17a38d6ac314b67d5138500ead7813\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5129eaaedfbc98d9f235725fef822117\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac82150854dce34f9e11609212b6e005\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\410cd51dd3e5bed7a8a155218f048e8a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fdad6dcdcc29d746b24c8caccb4cbfac\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b54fb0c6059ee7dc389f60abdda8ec9\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe9b95d8a6697af68b2d5f7ac373dbf1\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4c322a536b4daac9c41e337004b2e6e\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bdc7bc706e897acff2f14499f5bfb4e\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f08638e1dde6e22332c7577a3d42ad0e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fda11fec8614ce77eb020a2ee5962b7\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d0185bdd068c19419352542382085a2\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\efa1c837bfd6df7f2a0e3c9838eec21c\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d8e1a98e5f55c48abeb9df557b8e74\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77c62ff1a1f45b8ee5efd9b968142abb\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22a33290e46e324b7ea42269f4074b68\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b916a36faabcd2aa4aefa53eeb1dc16\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f094a9cd98d8e7c820fa68d9e743ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60633d2ab95fd9c4e770d5b5d6e03ec1\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ada06722eb3e600522b26251feb784e\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f0cd53ba688e453452768264eaec527\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:15:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:15:22-73
application
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:17:5-37:19
INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:17:5-37:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22a33290e46e324b7ea42269f4074b68\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22a33290e46e324b7ea42269f4074b68\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f094a9cd98d8e7c820fa68d9e743ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f094a9cd98d8e7c820fa68d9e743ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:24:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:22:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:20:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:23:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:21:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:18:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:25:9-53
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:19:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:26:9-44
activity#com.example.imagedownloader.MainActivity
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:27:9-36:20
	android:label
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:30:13-45
	android:exported
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:29:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:31:13-57
	android:name
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:28:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:32:13-35:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:33:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:33:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:34:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:34:27-74
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b5538d2e8925004503c2603d09c856e\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b5538d2e8925004503c2603d09c856e\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c1e17eae3721ff358c86731c9840490\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c1e17eae3721ff358c86731c9840490\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29de5ec125a2d20c25e57930259370ec\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29de5ec125a2d20c25e57930259370ec\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca8fe3c0abea6cb6fcafe94314e333b6\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca8fe3c0abea6cb6fcafe94314e333b6\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\12f1630e93cea9fb6dc93cb1d9d1b1e0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\12f1630e93cea9fb6dc93cb1d9d1b1e0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a843704273e916f72829d3ad6c60aff0\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a843704273e916f72829d3ad6c60aff0\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a806e04ac6a0c7130c07945d59d7d67\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a806e04ac6a0c7130c07945d59d7d67\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5afd894b7ef3fcabb4292eac3f6b4a9\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5afd894b7ef3fcabb4292eac3f6b4a9\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\24f3b1a12e8091b80c07f679a9055e5f\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\24f3b1a12e8091b80c07f679a9055e5f\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca069b81aae7f9145e18ceafffc36134\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca069b81aae7f9145e18ceafffc36134\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\74211e39ebbd4933cb6e7197727b70c4\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\74211e39ebbd4933cb6e7197727b70c4\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e42930b94b257198714fe1affe4d6e15\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\e42930b94b257198714fe1affe4d6e15\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2f6a2a7e6923b1656d524cff8fc96b9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b2f6a2a7e6923b1656d524cff8fc96b9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f999ebb768e3ed3825c90024a28be6a6\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f999ebb768e3ed3825c90024a28be6a6\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b261e901710f7c197c152ad6e2bf77a8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b261e901710f7c197c152ad6e2bf77a8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\995c80fea8f44a626044b31ada671c08\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\995c80fea8f44a626044b31ada671c08\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2fdda3cf75b79259a338795d156c6dd\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d2fdda3cf75b79259a338795d156c6dd\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\98c4a339e587ec56442454db77daf607\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\98c4a339e587ec56442454db77daf607\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\65471f26bcd7bc579f6b93911b8a3f55\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\65471f26bcd7bc579f6b93911b8a3f55\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b998037e39a80f60cd812ea9d31e7210\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\b998037e39a80f60cd812ea9d31e7210\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be834f8e59ef0ad7665a00a59dbbb85\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\4be834f8e59ef0ad7665a00a59dbbb85\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\486ba7343e577de335f7776b30351bd9\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\486ba7343e577de335f7776b30351bd9\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23abdce465d35a8007ecae11164b50f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e23abdce465d35a8007ecae11164b50f\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\328887870c685989ca0947d484ef0aef\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\328887870c685989ca0947d484ef0aef\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\863923f39d7044ae6db3fd633409ba80\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\863923f39d7044ae6db3fd633409ba80\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe7ec8d21979a321f05334a63126ddd5\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe7ec8d21979a321f05334a63126ddd5\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ef392a8d9f9fe53b60422c58d8938a3\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ef392a8d9f9fe53b60422c58d8938a3\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec7c3a0d685ca9f9b46a227fc236af1c\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec7c3a0d685ca9f9b46a227fc236af1c\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\40299ef8d0f30d949093f49976625db6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\40299ef8d0f30d949093f49976625db6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\276a500444e49e362f086f375956db77\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\276a500444e49e362f086f375956db77\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a5c063d720e7c5445d3611c3440a882\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a5c063d720e7c5445d3611c3440a882\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d7db48768896e526b918d011eba52b1\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d7db48768896e526b918d011eba52b1\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18bbead6eb51c8084bc422305b982042\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18bbead6eb51c8084bc422305b982042\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c3000d299da206cd42e78a175b21c3b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c3000d299da206cd42e78a175b21c3b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\051de53e7806c905f4312198682b9034\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\051de53e7806c905f4312198682b9034\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f70d57ff2e0c8718394d455a5842fbc7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f70d57ff2e0c8718394d455a5842fbc7\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\51c7789b7ce8f7e520fa1c4de77b2784\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\51c7789b7ce8f7e520fa1c4de77b2784\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fe2e9f8814488ebcc04f055e472bb64\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fe2e9f8814488ebcc04f055e472bb64\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bb35325efedde69ca4af8c2ec6f9423\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bb35325efedde69ca4af8c2ec6f9423\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb17a38d6ac314b67d5138500ead7813\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb17a38d6ac314b67d5138500ead7813\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5129eaaedfbc98d9f235725fef822117\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\5129eaaedfbc98d9f235725fef822117\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac82150854dce34f9e11609212b6e005\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac82150854dce34f9e11609212b6e005\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\410cd51dd3e5bed7a8a155218f048e8a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\410cd51dd3e5bed7a8a155218f048e8a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fdad6dcdcc29d746b24c8caccb4cbfac\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fdad6dcdcc29d746b24c8caccb4cbfac\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b54fb0c6059ee7dc389f60abdda8ec9\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b54fb0c6059ee7dc389f60abdda8ec9\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe9b95d8a6697af68b2d5f7ac373dbf1\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe9b95d8a6697af68b2d5f7ac373dbf1\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4c322a536b4daac9c41e337004b2e6e\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4c322a536b4daac9c41e337004b2e6e\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bdc7bc706e897acff2f14499f5bfb4e\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bdc7bc706e897acff2f14499f5bfb4e\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f08638e1dde6e22332c7577a3d42ad0e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f08638e1dde6e22332c7577a3d42ad0e\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fda11fec8614ce77eb020a2ee5962b7\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fda11fec8614ce77eb020a2ee5962b7\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d0185bdd068c19419352542382085a2\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\8d0185bdd068c19419352542382085a2\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\efa1c837bfd6df7f2a0e3c9838eec21c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\efa1c837bfd6df7f2a0e3c9838eec21c\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d8e1a98e5f55c48abeb9df557b8e74\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d8e1a98e5f55c48abeb9df557b8e74\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77c62ff1a1f45b8ee5efd9b968142abb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\77c62ff1a1f45b8ee5efd9b968142abb\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22a33290e46e324b7ea42269f4074b68\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22a33290e46e324b7ea42269f4074b68\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b916a36faabcd2aa4aefa53eeb1dc16\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b916a36faabcd2aa4aefa53eeb1dc16\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f094a9cd98d8e7c820fa68d9e743ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\19f094a9cd98d8e7c820fa68d9e743ce\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60633d2ab95fd9c4e770d5b5d6e03ec1\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60633d2ab95fd9c4e770d5b5d6e03ec1\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ada06722eb3e600522b26251feb784e\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ada06722eb3e600522b26251feb784e\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f0cd53ba688e453452768264eaec527\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f0cd53ba688e453452768264eaec527\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22a33290e46e324b7ea42269f4074b68\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\22a33290e46e324b7ea42269f4074b68\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.example.imagedownloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.imagedownloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
