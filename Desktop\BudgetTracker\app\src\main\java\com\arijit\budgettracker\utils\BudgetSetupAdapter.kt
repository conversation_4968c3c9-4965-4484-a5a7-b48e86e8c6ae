package com.arijit.budgettracker.utils

import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.arijit.budgettracker.R

class BudgetSetupAdapter(private val categories: List<String>) : RecyclerView.Adapter<BudgetSetupAdapter.BudgetSetupViewHolder>() {

    private val budgetAmounts = mutableMapOf<String, Double>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BudgetSetupViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_budget_setup, parent, false)
        return BudgetSetupViewHolder(view)
    }

    override fun onBindViewHolder(holder: BudgetSetupViewHolder, position: Int) {
        holder.bind(categories[position])
    }

    override fun getItemCount(): Int = categories.size

    fun getBudgetAmounts(): Map<String, Double> = budgetAmounts.toMap()

    inner class BudgetSetupViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val categoryText: TextView = itemView.findViewById(R.id.category_text)
        private val amountEdit: EditText = itemView.findViewById(R.id.amount_edit)

        fun bind(category: String) {
            categoryText.text = category
            
            // Clear previous text watcher to avoid conflicts
            amountEdit.tag?.let { 
                amountEdit.removeTextChangedListener(it as TextWatcher)
            }
            
            // Set existing value if any
            val existingAmount = budgetAmounts[category]
            if (existingAmount != null && existingAmount > 0) {
                amountEdit.setText(existingAmount.toInt().toString())
            } else {
                amountEdit.setText("")
            }

            // Create new text watcher
            val textWatcher = object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    val amountStr = s.toString().trim()
                    val amount = amountStr.toDoubleOrNull() ?: 0.0
                    budgetAmounts[category] = amount
                }
            }
            
            // Store text watcher in tag and add it
            amountEdit.tag = textWatcher
            amountEdit.addTextChangedListener(textWatcher)
        }
    }
}
