package com.arijit.budgettracker.models

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.arijit.budgettracker.db.Budget
import com.arijit.budgettracker.db.ExpenseDatabase
import com.arijit.budgettracker.db.SavingsGoal
import kotlinx.coroutines.launch
import java.util.Calendar

data class BudgetProgress(
    val category: String,
    val budgetAmount: Double,
    val spentAmount: Double,
    val progressPercentage: Int,
    val isOverBudget: Boolean
)

class BudgetViewModel(application: Application) : AndroidViewModel(application) {
    private val database = ExpenseDatabase.getDatabase(application)
    private val budgetDao = database.budgetDao()
    private val savingsGoalDao = database.savingsGoalDao()
    private val expenseDao = database.expenseDao()

    // Current month and year
    private val currentMonth = Calendar.getInstance().get(Calendar.MONTH) + 1
    private val currentYear = Calendar.getInstance().get(Calendar.YEAR)

    // LiveData for UI
    private val _currentMonthBudgetProgress = MutableLiveData<List<BudgetProgress>>()
    val currentMonthBudgetProgress: LiveData<List<BudgetProgress>> = _currentMonthBudgetProgress

    val activeSavingsGoals: LiveData<List<SavingsGoal>> = savingsGoalDao.getActiveSavingsGoals().asLiveData()
    val allBudgets: LiveData<List<Budget>> = budgetDao.getAllBudgets().asLiveData()

    init {
        refreshData()
    }

    fun refreshData() {
        viewModelScope.launch {
            calculateBudgetProgress()
        }
    }

    private suspend fun calculateBudgetProgress() {
        // Get data directly from DAOs using suspend functions
        val budgets = budgetDao.getBudgetsForMonthList(currentMonth, currentYear)
        val allExpenses = expenseDao.getAllExpensesList()

        // Filter expenses for current month (excluding savings)
        val currentMonthExpenses = allExpenses.filter { expense ->
            val expenseCalendar = Calendar.getInstance().apply { timeInMillis = expense.timeStamp }
            expenseCalendar.get(Calendar.MONTH) + 1 == currentMonth &&
                    expenseCalendar.get(Calendar.YEAR) == currentYear &&
                    expense.category != "Savings" && !expense.category.startsWith("Savings >")  // Exclude all savings from budget calculations
        }

        val budgetProgressList = budgets.map { budget ->
            val categoryExpenses = currentMonthExpenses.filter { it.category == budget.category }
            val spentAmount = categoryExpenses.sumOf { it.amount }
            val progressPercentage = if (budget.monthlyLimit > 0) {
                val rawPercentage = ((spentAmount / budget.monthlyLimit) * 100).toInt()
                minOf(rawPercentage, 100) // Cap at 100%
            } else 0
            val isOverBudget = spentAmount > budget.monthlyLimit

            BudgetProgress(
                category = budget.category,
                budgetAmount = budget.monthlyLimit,
                spentAmount = spentAmount,
                progressPercentage = progressPercentage,
                isOverBudget = isOverBudget
            )
        }
        _currentMonthBudgetProgress.postValue(budgetProgressList)
    }

    fun insertBudget(budget: Budget) {
        viewModelScope.launch {
            budgetDao.insertBudget(budget)
            refreshData()
        }
    }

    fun deleteBudgetsForMonth(month: Int, year: Int) {
        viewModelScope.launch {
            budgetDao.deleteBudgetsForMonth(month, year)
        }
    }

    fun deleteBudgetForCategoryAndMonth(category: String, month: Int, year: Int) {
        viewModelScope.launch {
            budgetDao.deleteBudgetForCategoryAndMonth(category, month, year)
        }
    }

    fun insertSavingsGoal(savingsGoal: SavingsGoal) {
        viewModelScope.launch {
            savingsGoalDao.insertSavingsGoal(savingsGoal)
        }
    }

    fun updateSavingsGoal(savingsGoal: SavingsGoal) {
        viewModelScope.launch {
            savingsGoalDao.updateSavingsGoal(savingsGoal)
        }
    }

    fun deleteBudget(budget: Budget) {
        viewModelScope.launch {
            budgetDao.deleteBudget(budget)
            refreshData()
        }
    }

    fun deleteSavingsGoal(savingsGoal: SavingsGoal) {
        viewModelScope.launch {
            savingsGoalDao.deleteSavingsGoal(savingsGoal)
        }
    }

    suspend fun updateSavingsGoalProgress(goalId: Int, amount: Double): Double {
        val goal = savingsGoalDao.getSavingsGoalById(goalId)
        return goal?.let {
            val remainingAmount = it.targetAmount - it.currentAmount
            val allocatedToGoal = minOf(amount, remainingAmount)
            val excessAmount = amount - allocatedToGoal

            val updatedGoal = it.copy(
                currentAmount = it.currentAmount + allocatedToGoal,
                isCompleted = (it.currentAmount + allocatedToGoal) >= it.targetAmount
            )
            savingsGoalDao.updateSavingsGoal(updatedGoal)

            // Return excess amount that should go to general savings
            excessAmount
        } ?: amount // If goal not found, all amount goes to general savings
    }

    suspend fun getTotalSavingsThisMonth(): Double {
        val allExpenses = expenseDao.getAllExpensesFlow().asLiveData().value ?: emptyList()

        // Filter savings transactions for current month
        val currentMonthSavings = allExpenses.filter { expense ->
            val expenseCalendar = Calendar.getInstance().apply { timeInMillis = expense.timeStamp }
            expenseCalendar.get(Calendar.MONTH) + 1 == currentMonth &&
                    expenseCalendar.get(Calendar.YEAR) == currentYear &&
                    expense.category == "Savings"
        }

        return currentMonthSavings.sumOf { it.amount }
    }

    suspend fun processSavingsTransaction(amount: Double, goalId: Int? = null) {
        if (goalId != null) {
            // Add to specific goal
            updateSavingsGoalProgress(goalId, amount)
        } else {
            // Add to first active goal (or could show selection dialog)
            val activeGoals = savingsGoalDao.getActiveSavingsGoals().asLiveData().value
            activeGoals?.firstOrNull()?.let { goal ->
                updateSavingsGoalProgress(goal.id, amount)
            }
        }
    }
}
