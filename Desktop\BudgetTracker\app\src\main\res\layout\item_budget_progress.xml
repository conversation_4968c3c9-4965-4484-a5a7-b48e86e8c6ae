<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/budget_progress_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="15dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/theme_surface"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Category Name -->
        <TextView
            android:id="@+id/category_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/montserrat_bold"
            android:text="Food"
            android:textColor="@color/theme_text_secondary"
            android:textSize="18sp"
            android:layout_marginBottom="4dp" />

        <!-- Amounts and Percentage Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/spent_amount_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_bold"
                android:text="₹2,500"
                android:textColor="@color/medium_green"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:text=" / "
                android:textColor="@color/theme_text_secondary"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/budget_amount_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/montserrat_regular"
                android:text="₹5,000"
                android:textColor="@color/theme_text_secondary"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/progress_percentage_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_bold"
                android:text="50%"
                android:textColor="@color/medium_green"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/budget_progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginBottom="8dp"
            android:max="100"
            android:progress="50"
            android:progressTint="@color/medium_green" />



    </LinearLayout>

</androidx.cardview.widget.CardView>
