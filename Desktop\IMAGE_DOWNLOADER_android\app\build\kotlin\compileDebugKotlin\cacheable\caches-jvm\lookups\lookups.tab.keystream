  Manifest android  READ_EXTERNAL_STORAGE android.Manifest.permission  READ_MEDIA_IMAGES android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  ImageDownloaderApp android.app.Activity  finish android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  
ContentValues android.content  Context android.content  insert android.content.ContentResolver  openOutputStream android.content.ContentResolver  Environment android.content.ContentValues  
MediaStore android.content.ContentValues  apply android.content.ContentValues  put android.content.ContentValues  ImageDownloaderApp android.content.Context  contentResolver android.content.Context  
setContent android.content.Context  ImageDownloaderApp android.content.ContextWrapper  
setContent android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bitmap android.graphics  CompressFormat android.graphics.Bitmap  compress android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  BitmapDrawable android.graphics.drawable  Drawable android.graphics.drawable  bitmap (android.graphics.drawable.BitmapDrawable  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  Environment 
android.os  SDK_INT android.os.Build.VERSION  Q android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  DIRECTORY_PICTURES android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  
MediaStore android.provider  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  ImageDownloaderApp  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  Toast android.widget  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  ImageDownloaderApp #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  ImageDownloaderApp -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AsyncImage "androidx.compose.foundation.layout  Bitmap "androidx.compose.foundation.layout  BitmapDrawable "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  
ContextCompat "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  Dispatchers "androidx.compose.foundation.layout  DownloadButton "androidx.compose.foundation.layout  Environment "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
FontFamily "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  ImageDownloaderApp "androidx.compose.foundation.layout  ImageLoader "androidx.compose.foundation.layout  ImageRequest "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MediaStore "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  PackageManager "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  
SuccessResult "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  all "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  arrayOf "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getFileNameFromUrl "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  isBlank "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  rememberScrollState "androidx.compose.foundation.layout  reversed "androidx.compose.foundation.layout  saveImageToGallery "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  
startsWith "androidx.compose.foundation.layout  substringAfterLast "androidx.compose.foundation.layout  
toMutableList "androidx.compose.foundation.layout  use "androidx.compose.foundation.layout  verticalScroll "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  withContext "androidx.compose.foundation.layout  wrapContentWidth "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  
AsyncImage +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  
FontFamily +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  rememberScrollState +androidx.compose.foundation.layout.BoxScope  reversed +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  verticalScroll +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  
AsyncImage .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentScale .androidx.compose.foundation.layout.ColumnScope  DownloadButton .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rememberScrollState .androidx.compose.foundation.layout.ColumnScope  reversed .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  
startsWith .androidx.compose.foundation.layout.ColumnScope  verticalScroll .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  wrapContentWidth .androidx.compose.foundation.layout.ColumnScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  DownloadButton +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  
PaddingValues +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  wrapContentWidth +androidx.compose.foundation.layout.RowScope  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
AsyncImage androidx.compose.material3  Bitmap androidx.compose.material3  BitmapDrawable androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Build androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  Context androidx.compose.material3  
ContextCompat androidx.compose.material3  Date androidx.compose.material3  Dispatchers androidx.compose.material3  DownloadButton androidx.compose.material3  Environment androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
FontFamily androidx.compose.material3  
FontWeight androidx.compose.material3  ImageDownloaderApp androidx.compose.material3  ImageLoader androidx.compose.material3  ImageRequest androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  Locale androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  
MediaStore androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedTextField androidx.compose.material3  PackageManager androidx.compose.material3  
PaddingValues androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  
SuccessResult androidx.compose.material3  System androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  all androidx.compose.material3  apply androidx.compose.material3  arrayOf androidx.compose.material3  
background androidx.compose.material3  buttonColors androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getFileNameFromUrl androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  isBlank androidx.compose.material3  
isNotBlank androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  rememberScrollState androidx.compose.material3  reversed androidx.compose.material3  saveImageToGallery androidx.compose.material3  setValue androidx.compose.material3  spacedBy androidx.compose.material3  
startsWith androidx.compose.material3  substringAfterLast androidx.compose.material3  
toMutableList androidx.compose.material3  use androidx.compose.material3  verticalScroll androidx.compose.material3  weight androidx.compose.material3  withContext androidx.compose.material3  wrapContentWidth androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  
AsyncImage androidx.compose.runtime  Bitmap androidx.compose.runtime  BitmapDrawable androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Build androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  Context androidx.compose.runtime  
ContextCompat androidx.compose.runtime  Date androidx.compose.runtime  Dispatchers androidx.compose.runtime  DownloadButton androidx.compose.runtime  Environment androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
FontFamily androidx.compose.runtime  
FontWeight androidx.compose.runtime  ImageDownloaderApp androidx.compose.runtime  ImageLoader androidx.compose.runtime  ImageRequest androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  Locale androidx.compose.runtime  Manifest androidx.compose.runtime  
MediaStore androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PackageManager androidx.compose.runtime  
PaddingValues androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Spacer androidx.compose.runtime  String androidx.compose.runtime  
SuccessResult androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  Unit androidx.compose.runtime  all androidx.compose.runtime  apply androidx.compose.runtime  arrayOf androidx.compose.runtime  
background androidx.compose.runtime  buttonColors androidx.compose.runtime  contains androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getFileNameFromUrl androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  isBlank androidx.compose.runtime  
isNotBlank androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  rememberScrollState androidx.compose.runtime  reversed androidx.compose.runtime  saveImageToGallery androidx.compose.runtime  setValue androidx.compose.runtime  spacedBy androidx.compose.runtime  
startsWith androidx.compose.runtime  substringAfterLast androidx.compose.runtime  
toMutableList androidx.compose.runtime  use androidx.compose.runtime  verticalScroll androidx.compose.runtime  weight androidx.compose.runtime  withContext androidx.compose.runtime  wrapContentWidth androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  wrapContentWidth androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  verticalScroll &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Gray ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Fit 'androidx.compose.ui.layout.ContentScale  Fit 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Uri +androidx.compose.ui.text.input.KeyboardType  Uri 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  ImageDownloaderApp #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ImageLoader coil  execute coil.ImageLoader  
AsyncImage coil.compose  ImageRequest coil.request  ImageResult coil.request  
SuccessResult coil.request  Builder coil.request.ImageRequest  build !coil.request.ImageRequest.Builder  data !coil.request.ImageRequest.Builder  drawable coil.request.ImageResult  drawable coil.request.SuccessResult  ActivityResultContracts com.example.imagedownloader  	Alignment com.example.imagedownloader  Arrangement com.example.imagedownloader  
AsyncImage com.example.imagedownloader  Bitmap com.example.imagedownloader  BitmapDrawable com.example.imagedownloader  Boolean com.example.imagedownloader  Box com.example.imagedownloader  Build com.example.imagedownloader  Bundle com.example.imagedownloader  Button com.example.imagedownloader  ButtonDefaults com.example.imagedownloader  Color com.example.imagedownloader  Column com.example.imagedownloader  ComponentActivity com.example.imagedownloader  
Composable com.example.imagedownloader  ContentScale com.example.imagedownloader  Context com.example.imagedownloader  
ContextCompat com.example.imagedownloader  Date com.example.imagedownloader  Dispatchers com.example.imagedownloader  DownloadButton com.example.imagedownloader  Environment com.example.imagedownloader  	Exception com.example.imagedownloader  ExperimentalMaterial3Api com.example.imagedownloader  
FontFamily com.example.imagedownloader  
FontWeight com.example.imagedownloader  ImageDownloaderApp com.example.imagedownloader  ImageLoader com.example.imagedownloader  ImageRequest com.example.imagedownloader  KeyboardOptions com.example.imagedownloader  KeyboardType com.example.imagedownloader  LaunchedEffect com.example.imagedownloader  Locale com.example.imagedownloader  MainActivity com.example.imagedownloader  Manifest com.example.imagedownloader  
MediaStore com.example.imagedownloader  Modifier com.example.imagedownloader  OptIn com.example.imagedownloader  OutlinedTextField com.example.imagedownloader  PackageManager com.example.imagedownloader  
PaddingValues com.example.imagedownloader  RoundedCornerShape com.example.imagedownloader  Row com.example.imagedownloader  Spacer com.example.imagedownloader  String com.example.imagedownloader  
SuccessResult com.example.imagedownloader  System com.example.imagedownloader  Text com.example.imagedownloader  	TextAlign com.example.imagedownloader  Unit com.example.imagedownloader  all com.example.imagedownloader  apply com.example.imagedownloader  arrayOf com.example.imagedownloader  
background com.example.imagedownloader  buttonColors com.example.imagedownloader  contains com.example.imagedownloader  	emptyList com.example.imagedownloader  fillMaxSize com.example.imagedownloader  fillMaxWidth com.example.imagedownloader  forEach com.example.imagedownloader  getFileNameFromUrl com.example.imagedownloader  getValue com.example.imagedownloader  height com.example.imagedownloader  isBlank com.example.imagedownloader  
isNotBlank com.example.imagedownloader  let com.example.imagedownloader  listOf com.example.imagedownloader  mutableStateOf com.example.imagedownloader  padding com.example.imagedownloader  provideDelegate com.example.imagedownloader  remember com.example.imagedownloader  rememberCoroutineScope com.example.imagedownloader  rememberScrollState com.example.imagedownloader  reversed com.example.imagedownloader  saveImageToGallery com.example.imagedownloader  setValue com.example.imagedownloader  spacedBy com.example.imagedownloader  
startsWith com.example.imagedownloader  substringAfterLast com.example.imagedownloader  
toMutableList com.example.imagedownloader  use com.example.imagedownloader  verticalScroll com.example.imagedownloader  weight com.example.imagedownloader  withContext com.example.imagedownloader  wrapContentWidth com.example.imagedownloader  ImageDownloaderApp (com.example.imagedownloader.MainActivity  
setContent (com.example.imagedownloader.MainActivity  Boolean $com.example.imagedownloader.ui.theme  Build $com.example.imagedownloader.ui.theme  
Composable $com.example.imagedownloader.ui.theme  DarkColorScheme $com.example.imagedownloader.ui.theme  
FontFamily $com.example.imagedownloader.ui.theme  
FontWeight $com.example.imagedownloader.ui.theme  ImageDownloaderTheme $com.example.imagedownloader.ui.theme  LightColorScheme $com.example.imagedownloader.ui.theme  Pink40 $com.example.imagedownloader.ui.theme  Pink80 $com.example.imagedownloader.ui.theme  Purple40 $com.example.imagedownloader.ui.theme  Purple80 $com.example.imagedownloader.ui.theme  PurpleGrey40 $com.example.imagedownloader.ui.theme  PurpleGrey80 $com.example.imagedownloader.ui.theme  
Typography $com.example.imagedownloader.ui.theme  Unit $com.example.imagedownloader.ui.theme  File java.io  FileOutputStream java.io  OutputStream java.io  absolutePath java.io.File  exists java.io.File  mkdirs java.io.File  use java.io.FileOutputStream  use java.io.OutputStream  	Exception 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  ActivityResultContracts 	java.util  	Alignment 	java.util  Arrangement 	java.util  
AsyncImage 	java.util  Bitmap 	java.util  BitmapDrawable 	java.util  Boolean 	java.util  Box 	java.util  Build 	java.util  Bundle 	java.util  Button 	java.util  ButtonDefaults 	java.util  Color 	java.util  Column 	java.util  
Comparator 	java.util  ComponentActivity 	java.util  
Composable 	java.util  ContentScale 	java.util  Context 	java.util  
ContextCompat 	java.util  Date 	java.util  Dispatchers 	java.util  DownloadButton 	java.util  Environment 	java.util  	Exception 	java.util  ExperimentalMaterial3Api 	java.util  
FontFamily 	java.util  
FontWeight 	java.util  ImageDownloaderApp 	java.util  ImageLoader 	java.util  ImageRequest 	java.util  KeyboardOptions 	java.util  KeyboardType 	java.util  LaunchedEffect 	java.util  Locale 	java.util  Manifest 	java.util  
MediaStore 	java.util  Modifier 	java.util  OptIn 	java.util  OutlinedTextField 	java.util  PackageManager 	java.util  
PaddingValues 	java.util  RoundedCornerShape 	java.util  Row 	java.util  Spacer 	java.util  String 	java.util  
SuccessResult 	java.util  System 	java.util  Text 	java.util  	TextAlign 	java.util  Unit 	java.util  all 	java.util  apply 	java.util  arrayOf 	java.util  
background 	java.util  buttonColors 	java.util  contains 	java.util  	emptyList 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  forEach 	java.util  getFileNameFromUrl 	java.util  getValue 	java.util  height 	java.util  isBlank 	java.util  
isNotBlank 	java.util  let 	java.util  listOf 	java.util  mutableStateOf 	java.util  padding 	java.util  provideDelegate 	java.util  remember 	java.util  rememberCoroutineScope 	java.util  rememberScrollState 	java.util  reversed 	java.util  saveImageToGallery 	java.util  setValue 	java.util  spacedBy 	java.util  
startsWith 	java.util  substringAfterLast 	java.util  
toMutableList 	java.util  use 	java.util  verticalScroll 	java.util  weight 	java.util  withContext 	java.util  wrapContentWidth 	java.util  
getDefault java.util.Locale  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  OptIn kotlin  apply kotlin  arrayOf kotlin  let kotlin  use kotlin  all kotlin.Array  not kotlin.Boolean  sp 
kotlin.Double  invoke kotlin.Function0  	compareTo 
kotlin.Int  contains 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  plus 
kotlin.String  
startsWith 
kotlin.String  substringAfterLast 
kotlin.String  message kotlin.Throwable  
Collection kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  all kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  listOf kotlin.collections  reversed kotlin.collections  
toMutableList kotlin.collections  all kotlin.collections.Collection  isEmpty kotlin.collections.List  reversed kotlin.collections.List  
toMutableList kotlin.collections.List  values kotlin.collections.Map  add kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  reversed kotlin.comparisons  SuspendFunction1 kotlin.coroutines  
startsWith 	kotlin.io  use 	kotlin.io  CharProgression 
kotlin.ranges  IntProgression 
kotlin.ranges  LongProgression 
kotlin.ranges  UIntProgression 
kotlin.ranges  ULongProgression 
kotlin.ranges  contains 
kotlin.ranges  reversed 
kotlin.ranges  KMutableProperty0 kotlin.reflect  all kotlin.sequences  contains kotlin.sequences  forEach kotlin.sequences  
toMutableList kotlin.sequences  all kotlin.text  contains kotlin.text  forEach kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  reversed kotlin.text  
startsWith kotlin.text  substringAfterLast kotlin.text  
toMutableList kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Build !kotlinx.coroutines.CoroutineScope  
ContextCompat !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  ImageLoader !kotlinx.coroutines.CoroutineScope  ImageRequest !kotlinx.coroutines.CoroutineScope  Manifest !kotlinx.coroutines.CoroutineScope  PackageManager !kotlinx.coroutines.CoroutineScope  all !kotlinx.coroutines.CoroutineScope  arrayOf !kotlinx.coroutines.CoroutineScope  getFileNameFromUrl !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  saveImageToGallery !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                