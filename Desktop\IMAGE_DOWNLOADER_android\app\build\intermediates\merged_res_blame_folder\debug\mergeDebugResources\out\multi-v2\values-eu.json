{"logs": [{"outputFile": "com.example.imagedownloader.app-mergeDebugResources-52:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\273013bf131dc793dae0cb2367ffa634\\transformed\\core-1.15.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2850,2948,3051,3151,3254,3359,3462,11405", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "2943,3046,3146,3249,3354,3457,3576,11501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\b261e901710f7c197c152ad6e2bf77a8\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11771,11857", "endColumns": "85,88", "endOffsets": "11852,11941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\24f3b1a12e8091b80c07f679a9055e5f\\transformed\\material3-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4864,4951,5039,5149,5229,5314,5409,5512,5603,5702,5791,5899,5999,6105,6223,6303,6407", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4859,4946,5034,5144,5224,5309,5404,5507,5598,5697,5786,5894,5994,6100,6218,6298,6402,6497"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4213,4360,4507,4634,4779,4908,5006,5121,5261,5380,5525,5609,5714,5810,5910,6029,6150,6260,6403,6547,6682,6873,6998,7120,7244,7366,7463,7560,7688,7823,7921,8024,8130,8277,8428,8536,8636,8712,8808,8903,9022,9109,9197,9307,9387,9472,9567,9670,9761,9860,9949,10057,10157,10263,10381,10461,10565", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "4355,4502,4629,4774,4903,5001,5116,5256,5375,5520,5604,5709,5805,5905,6024,6145,6255,6398,6542,6677,6868,6993,7115,7239,7361,7458,7555,7683,7818,7916,8019,8125,8272,8423,8531,8631,8707,8803,8898,9017,9104,9192,9302,9382,9467,9562,9665,9756,9855,9944,10052,10152,10258,10376,10456,10560,10655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\a843704273e916f72829d3ad6c60aff0\\transformed\\appcompat-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,11011", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,11089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\863923f39d7044ae6db3fd633409ba80\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,998,1088,1162,1239,1321,1399,1476,1544", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,993,1083,1157,1234,1316,1394,1471,1539,1659"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3581,3672,3753,3854,3958,4050,4126,10660,10749,10833,10921,11094,11168,11245,11327,11506,11583,11651", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,76,81,77,76,67,119", "endOffsets": "3667,3748,3849,3953,4045,4121,4208,10744,10828,10916,11006,11163,11240,11322,11400,11578,11646,11766"}}]}]}