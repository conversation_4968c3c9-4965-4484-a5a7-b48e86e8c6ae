<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/theme_background"
    tools:context=".AddGoalActivity">

    <!-- Header -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/back_btn"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="Back"
            app:tint="@color/light_green" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/montserrat_bold"
            android:text="Add Savings Goal"
            android:textColor="@color/light_green"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/save_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/tick_bg"
            android:fontFamily="@font/montserrat_bold"
            android:paddingHorizontal="20dp"
            android:paddingVertical="8dp"
            android:text="Save"
            android:textColor="@color/light_green"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Form Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header"
        android:layout_marginTop="20dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="20dp"
            android:paddingBottom="20dp">

            <!-- Goal Title -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/light_green"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat_bold"
                        android:text="Goal Title"
                        android:textColor="@color/dark_green"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp" />

                    <EditText
                        android:id="@+id/goal_title_edit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/montserrat_regular"
                        android:hint="e.g., Vacation Fund, New Car"
                        android:textColor="@color/dark_green"
                        android:textColorHint="@color/medium_green"
                        android:textSize="16sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Target Amount -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/light_green"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat_bold"
                        android:text="Target Amount"
                        android:textColor="@color/dark_green"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp" />

                    <EditText
                        android:id="@+id/target_amount_edit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:fontFamily="@font/montserrat_regular"
                        android:hint="Enter amount"
                        android:inputType="numberDecimal"
                        android:textColor="@color/dark_green"
                        android:textColorHint="@color/medium_green"
                        android:textSize="16sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Target Date -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="15dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/light_green"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat_bold"
                        android:text="Target Date"
                        android:textColor="@color/dark_green"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/target_date_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/montserrat_regular"
                        android:text="Select target date"
                        android:textColor="@color/medium_green"
                        android:textSize="16sp"
                        android:padding="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</RelativeLayout>
