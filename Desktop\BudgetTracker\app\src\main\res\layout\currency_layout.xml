<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/catg_layout_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:paddingVertical="20dp"
        android:paddingHorizontal="20dp">

        <TextView
            android:id="@+id/inr"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/dark_green"
            android:fontFamily="@font/montserrat_regular"
            android:textSize="18sp"
            android:text="₹ - INR" />

        <TextView
            android:id="@+id/usd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/dark_green"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/montserrat_regular"
            android:textSize="18sp"
            android:text="$ - USD" />

        <TextView
            android:id="@+id/cny"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/dark_green"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/montserrat_regular"
            android:textSize="18sp"
            android:text="¥ - CNY" />

        <TextView
            android:id="@+id/jpy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/dark_green"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/montserrat_regular"
            android:textSize="18sp"
            android:text="¥ - JPY" />

        <TextView
            android:id="@+id/rub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/dark_green"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/montserrat_regular"
            android:textSize="18sp"
            android:text="₽ - RUB" />

        <TextView
            android:id="@+id/eur"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/dark_green"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/montserrat_regular"
            android:textSize="18sp"
            android:text="€ - EUR" />

    </LinearLayout>

</RelativeLayout>