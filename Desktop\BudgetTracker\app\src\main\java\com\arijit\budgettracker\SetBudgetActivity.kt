package com.arijit.budgettracker

import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.arijit.budgettracker.db.Budget
import com.arijit.budgettracker.models.BudgetViewModel
import com.arijit.budgettracker.utils.BudgetSetupAdapter
import com.arijit.budgettracker.utils.Vibration
import kotlinx.coroutines.launch
import java.util.Calendar
import android.content.Context
import androidx.appcompat.app.AppCompatDelegate

class SetBudgetActivity : AppCompatActivity() {

    private lateinit var viewModel: BudgetViewModel
    private lateinit var budgetSetupAdapter: BudgetSetupAdapter
    private lateinit var backButton: ImageView
    private lateinit var saveButton: TextView
    private lateinit var recyclerView: RecyclerView

    // Categories matching your existing expense categories
    private val categories = listOf(
        "Transport", "Entertainment", "Food", "Housing", 
        "Pet", "Health", "Shopping", "Miscellaneous"
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        // Apply saved theme before calling super.onCreate()
        val sharedPrefs = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        val isDarkMode = sharedPrefs.getBoolean("dark_mode", false)
        if (isDarkMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
        }

        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_set_budget)
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        initViews()
        setupRecyclerView()
        setupClickListeners()
        
        viewModel = ViewModelProvider(this)[BudgetViewModel::class.java]
    }

    private fun initViews() {
        backButton = findViewById(R.id.back_btn)
        saveButton = findViewById(R.id.save_btn)
        recyclerView = findViewById(R.id.budget_setup_rv)
    }

    private fun setupRecyclerView() {
        budgetSetupAdapter = BudgetSetupAdapter(categories)
        recyclerView.adapter = budgetSetupAdapter
        recyclerView.layoutManager = LinearLayoutManager(this)
    }

    private fun setupClickListeners() {
        backButton.setOnClickListener {
            Vibration.vibrate(this, 50)
            finish()
        }

        saveButton.setOnClickListener {
            Vibration.vibrate(this, 100)
            saveBudgets()
        }
    }

    private fun saveBudgets() {
        val budgetAmounts = budgetSetupAdapter.getBudgetAmounts()
        val currentMonth = Calendar.getInstance().get(Calendar.MONTH) + 1
        val currentYear = Calendar.getInstance().get(Calendar.YEAR)

        lifecycleScope.launch {
            budgetAmounts.forEach { (category, amount) ->
                // First, delete existing budget for this specific category/month/year
                viewModel.deleteBudgetForCategoryAndMonth(category, currentMonth, currentYear)

                if (amount > 0) {
                    val budget = Budget(
                        category = category,
                        monthlyLimit = amount,
                        month = currentMonth,
                        year = currentYear
                    )
                    viewModel.insertBudget(budget)
                }
            }
            finish()
        }
    }
}
