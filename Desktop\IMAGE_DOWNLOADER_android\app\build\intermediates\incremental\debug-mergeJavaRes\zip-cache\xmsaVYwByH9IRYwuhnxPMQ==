[{"key": "coil/ComponentRegistry$Builder.class", "name": "coil/ComponentRegistry$Builder.class", "size": 7615, "crc": 816450964}, {"key": "coil/ComponentRegistry.class", "name": "coil/ComponentRegistry.class", "size": 10762, "crc": -2139326186}, {"key": "coil/EventListener$Companion$NONE$1.class", "name": "coil/EventListener$Companion$NONE$1.class", "size": 617, "crc": 25536047}, {"key": "coil/EventListener$Companion.class", "name": "coil/EventListener$Companion.class", "size": 736, "crc": 2102898964}, {"key": "coil/EventListener$DefaultImpls.class", "name": "coil/EventListener$DefaultImpls.class", "size": 5522, "crc": 669417242}, {"key": "coil/EventListener$Factory$Companion.class", "name": "coil/EventListener$Factory$Companion.class", "size": 810, "crc": 1086544126}, {"key": "coil/EventListener$Factory.class", "name": "coil/EventListener$Factory.class", "size": 1558, "crc": -752206957}, {"key": "coil/EventListener.class", "name": "coil/EventListener.class", "size": 8552, "crc": 1696610288}, {"key": "coil/ImageLoader$Builder.class", "name": "coil/ImageLoader$Builder.class", "size": 22860, "crc": 1954394268}, {"key": "coil/ImageLoader.class", "name": "coil/ImageLoader.class", "size": 1892, "crc": 741583792}, {"key": "coil/ImageLoaders$executeBlocking$1.class", "name": "coil/ImageLoaders$executeBlocking$1.class", "size": 3338, "crc": -488809524}, {"key": "coil/ImageLoaders.class", "name": "coil/ImageLoaders.class", "size": 1747, "crc": -1477461791}, {"key": "coil/RealImageLoader$Companion.class", "name": "coil/RealImageLoader$Companion.class", "size": 918, "crc": 1793573261}, {"key": "coil/RealImageLoader$enqueue$job$1.class", "name": "coil/RealImageLoader$enqueue$job$1.class", "size": 3821, "crc": -791026813}, {"key": "coil/RealImageLoader$execute$2$job$1.class", "name": "coil/RealImageLoader$execute$2$job$1.class", "size": 3301, "crc": 1428270847}, {"key": "coil/RealImageLoader$execute$2.class", "name": "coil/RealImageLoader$execute$2.class", "size": 4507, "crc": -278987350}, {"key": "coil/RealImageLoader$execute$3.class", "name": "coil/RealImageLoader$execute$3.class", "size": 3330, "crc": -1008388994}, {"key": "coil/RealImageLoader$executeMain$1.class", "name": "coil/RealImageLoader$executeMain$1.class", "size": 1946, "crc": -1213757900}, {"key": "coil/RealImageLoader$executeMain$result$1.class", "name": "coil/RealImageLoader$executeMain$result$1.class", "size": 4188, "crc": -75613056}, {"key": "coil/RealImageLoader$special$$inlined$CoroutineExceptionHandler$1.class", "name": "coil/RealImageLoader$special$$inlined$CoroutineExceptionHandler$1.class", "size": 3149, "crc": -523345920}, {"key": "coil/RealImageLoader.class", "name": "coil/RealImageLoader.class", "size": 28845, "crc": -1791162645}, {"key": "coil/annotation/ExperimentalCoilApi.class", "name": "coil/annotation/ExperimentalCoilApi.class", "size": 920, "crc": 1284824629}, {"key": "coil/decode/AssetMetadata.class", "name": "coil/decode/AssetMetadata.class", "size": 1593, "crc": -433577836}, {"key": "coil/decode/BitmapFactoryDecoder$Companion.class", "name": "coil/decode/BitmapFactoryDecoder$Companion.class", "size": 878, "crc": 1330945429}, {"key": "coil/decode/BitmapFactoryDecoder$ExceptionCatchingSource.class", "name": "coil/decode/BitmapFactoryDecoder$ExceptionCatchingSource.class", "size": 1559, "crc": -338860104}, {"key": "coil/decode/BitmapFactoryDecoder$Factory.class", "name": "coil/decode/BitmapFactoryDecoder$Factory.class", "size": 3271, "crc": 1665406233}, {"key": "coil/decode/BitmapFactoryDecoder$decode$1.class", "name": "coil/decode/BitmapFactoryDecoder$decode$1.class", "size": 1684, "crc": -2035544011}, {"key": "coil/decode/BitmapFactoryDecoder.class", "name": "coil/decode/BitmapFactoryDecoder.class", "size": 14915, "crc": 1271487124}, {"key": "coil/decode/ContentMetadata.class", "name": "coil/decode/ContentMetadata.class", "size": 1047, "crc": -1784177433}, {"key": "coil/decode/DataSource.class", "name": "coil/decode/DataSource.class", "size": 1871, "crc": 1860941449}, {"key": "coil/decode/DecodeResult.class", "name": "coil/decode/DecodeResult.class", "size": 2230, "crc": 1819730877}, {"key": "coil/decode/DecodeUtils$WhenMappings.class", "name": "coil/decode/DecodeUtils$WhenMappings.class", "size": 691, "crc": 1587182072}, {"key": "coil/decode/DecodeUtils.class", "name": "coil/decode/DecodeUtils.class", "size": 2952, "crc": 1439996712}, {"key": "coil/decode/Decoder$Factory.class", "name": "coil/decode/Decoder$Factory.class", "size": 948, "crc": -553680139}, {"key": "coil/decode/Decoder.class", "name": "coil/decode/Decoder.class", "size": 849, "crc": 587305691}, {"key": "coil/decode/ExifData$Companion.class", "name": "coil/decode/ExifData$Companion.class", "size": 839, "crc": 1638325581}, {"key": "coil/decode/ExifData.class", "name": "coil/decode/ExifData.class", "size": 1296, "crc": -1875534598}, {"key": "coil/decode/ExifInterfaceInputStream.class", "name": "coil/decode/ExifInterfaceInputStream.class", "size": 1835, "crc": -293759549}, {"key": "coil/decode/ExifOrientationPolicy.class", "name": "coil/decode/ExifOrientationPolicy.class", "size": 1912, "crc": -886795302}, {"key": "coil/decode/ExifUtils.class", "name": "coil/decode/ExifUtils.class", "size": 4872, "crc": -102039747}, {"key": "coil/decode/ExifUtilsKt$WhenMappings.class", "name": "coil/decode/ExifUtilsKt$WhenMappings.class", "size": 812, "crc": 349031245}, {"key": "coil/decode/ExifUtilsKt.class", "name": "coil/decode/ExifUtilsKt.class", "size": 2113, "crc": 111204272}, {"key": "coil/decode/FileImageSource.class", "name": "coil/decode/FileImageSource.class", "size": 4116, "crc": -559299340}, {"key": "coil/decode/ImageSource$Metadata.class", "name": "coil/decode/ImageSource$Metadata.class", "size": 672, "crc": 1768116489}, {"key": "coil/decode/ImageSource.class", "name": "coil/decode/ImageSource.class", "size": 1730, "crc": -1713225824}, {"key": "coil/decode/ImageSources.class", "name": "coil/decode/ImageSources.class", "size": 5255, "crc": 1548068881}, {"key": "coil/decode/ResourceMetadata.class", "name": "coil/decode/ResourceMetadata.class", "size": 1458, "crc": 39613531}, {"key": "coil/decode/SourceImageSource.class", "name": "coil/decode/SourceImageSource.class", "size": 7223, "crc": -722489302}, {"key": "coil/disk/DiskCache$Builder.class", "name": "coil/disk/DiskCache$Builder.class", "size": 6383, "crc": -1478481882}, {"key": "coil/disk/DiskCache$Editor.class", "name": "coil/disk/DiskCache$Editor.class", "size": 1299, "crc": -434922221}, {"key": "coil/disk/DiskCache$Snapshot.class", "name": "coil/disk/DiskCache$Snapshot.class", "size": 1334, "crc": -1553260671}, {"key": "coil/disk/DiskCache.class", "name": "coil/disk/DiskCache.class", "size": 2376, "crc": 1069689492}, {"key": "coil/disk/DiskLruCache$Companion.class", "name": "coil/disk/DiskLruCache$Companion.class", "size": 1780, "crc": 124206684}, {"key": "coil/disk/DiskLruCache$Editor.class", "name": "coil/disk/DiskLruCache$Editor.class", "size": 4731, "crc": 1749211749}, {"key": "coil/disk/DiskLruCache$Entry.class", "name": "coil/disk/DiskLruCache$Entry.class", "size": 6869, "crc": -1914456261}, {"key": "coil/disk/DiskLruCache$Snapshot.class", "name": "coil/disk/DiskLruCache$Snapshot.class", "size": 3534, "crc": 896190419}, {"key": "coil/disk/DiskLruCache$fileSystem$1.class", "name": "coil/disk/DiskLruCache$fileSystem$1.class", "size": 1722, "crc": -1011047696}, {"key": "coil/disk/DiskLruCache$launchCleanup$1.class", "name": "coil/disk/DiskLruCache$launchCleanup$1.class", "size": 3873, "crc": -1492293021}, {"key": "coil/disk/DiskLruCache.class", "name": "coil/disk/DiskLruCache.class", "size": 24632, "crc": 2096484703}, {"key": "coil/disk/FaultHidingSink.class", "name": "coil/disk/FaultHidingSink.class", "size": 2141, "crc": -80900830}, {"key": "coil/disk/RealDiskCache$Companion.class", "name": "coil/disk/RealDiskCache$Companion.class", "size": 871, "crc": -1869901474}, {"key": "coil/disk/RealDiskCache$RealEditor.class", "name": "coil/disk/RealDiskCache$RealEditor.class", "size": 3049, "crc": -1409744652}, {"key": "coil/disk/RealDiskCache$RealSnapshot.class", "name": "coil/disk/RealDiskCache$RealSnapshot.class", "size": 2951, "crc": -1913705983}, {"key": "coil/disk/RealDiskCache.class", "name": "coil/disk/RealDiskCache.class", "size": 5091, "crc": 1913419625}, {"key": "coil/drawable/CrossfadeDrawable$Companion.class", "name": "coil/drawable/CrossfadeDrawable$Companion.class", "size": 975, "crc": -1885680597}, {"key": "coil/drawable/CrossfadeDrawable.class", "name": "coil/drawable/CrossfadeDrawable.class", "size": 17707, "crc": 295762093}, {"key": "coil/fetch/AssetUriFetcher$Factory.class", "name": "coil/fetch/AssetUriFetcher$Factory.class", "size": 1757, "crc": -2129130279}, {"key": "coil/fetch/AssetUriFetcher.class", "name": "coil/fetch/AssetUriFetcher.class", "size": 3183, "crc": -944647872}, {"key": "coil/fetch/BitmapFetcher$Factory.class", "name": "coil/fetch/BitmapFetcher$Factory.class", "size": 1642, "crc": 155035037}, {"key": "coil/fetch/BitmapFetcher.class", "name": "coil/fetch/BitmapFetcher.class", "size": 3006, "crc": -1402962806}, {"key": "coil/fetch/ByteBufferFetcher$Factory.class", "name": "coil/fetch/ByteBufferFetcher$Factory.class", "size": 1638, "crc": 2113325791}, {"key": "coil/fetch/ByteBufferFetcher.class", "name": "coil/fetch/ByteBufferFetcher.class", "size": 2853, "crc": -87133054}, {"key": "coil/fetch/ContentUriFetcher$Factory.class", "name": "coil/fetch/ContentUriFetcher$Factory.class", "size": 2013, "crc": 1167898876}, {"key": "coil/fetch/ContentUriFetcher.class", "name": "coil/fetch/ContentUriFetcher.class", "size": 6975, "crc": -54280297}, {"key": "coil/fetch/DrawableFetcher$Factory.class", "name": "coil/fetch/DrawableFetcher$Factory.class", "size": 1705, "crc": 197987351}, {"key": "coil/fetch/DrawableFetcher.class", "name": "coil/fetch/DrawableFetcher.class", "size": 3882, "crc": -2079781606}, {"key": "coil/fetch/DrawableResult.class", "name": "coil/fetch/DrawableResult.class", "size": 2780, "crc": 829351167}, {"key": "coil/fetch/FetchResult.class", "name": "coil/fetch/FetchResult.class", "size": 769, "crc": -1985783324}, {"key": "coil/fetch/Fetcher$Factory.class", "name": "coil/fetch/Fetcher$Factory.class", "size": 1068, "crc": -1009291742}, {"key": "coil/fetch/Fetcher.class", "name": "coil/fetch/Fetcher.class", "size": 841, "crc": 1385550321}, {"key": "coil/fetch/FileFetcher$Factory.class", "name": "coil/fetch/FileFetcher$Factory.class", "size": 1556, "crc": -1106084745}, {"key": "coil/fetch/FileFetcher.class", "name": "coil/fetch/FileFetcher.class", "size": 2142, "crc": 587601107}, {"key": "coil/fetch/HttpUriFetcher$Companion.class", "name": "coil/fetch/HttpUriFetcher$Companion.class", "size": 1003, "crc": 304501235}, {"key": "coil/fetch/HttpUriFetcher$Factory.class", "name": "coil/fetch/HttpUriFetcher$Factory.class", "size": 2798, "crc": -2045811873}, {"key": "coil/fetch/HttpUriFetcher$executeNetworkRequest$1.class", "name": "coil/fetch/HttpUriFetcher$executeNetworkRequest$1.class", "size": 1745, "crc": 329352089}, {"key": "coil/fetch/HttpUriFetcher$fetch$1.class", "name": "coil/fetch/HttpUriFetcher$fetch$1.class", "size": 1675, "crc": -1167875406}, {"key": "coil/fetch/HttpUriFetcher.class", "name": "coil/fetch/HttpUriFetcher.class", "size": 22748, "crc": 2036904601}, {"key": "coil/fetch/ResourceUriFetcher$Companion.class", "name": "coil/fetch/ResourceUriFetcher$Companion.class", "size": 857, "crc": -346068147}, {"key": "coil/fetch/ResourceUriFetcher$Factory.class", "name": "coil/fetch/ResourceUriFetcher$Factory.class", "size": 2026, "crc": -273417978}, {"key": "coil/fetch/ResourceUriFetcher.class", "name": "coil/fetch/ResourceUriFetcher.class", "size": 8355, "crc": 1253697355}, {"key": "coil/fetch/SourceResult.class", "name": "coil/fetch/SourceResult.class", "size": 2910, "crc": 1041400777}, {"key": "coil/intercept/EngineInterceptor$Companion.class", "name": "coil/intercept/EngineInterceptor$Companion.class", "size": 855, "crc": 1013600452}, {"key": "coil/intercept/EngineInterceptor$ExecuteResult.class", "name": "coil/intercept/EngineInterceptor$ExecuteResult.class", "size": 2578, "crc": 1765650101}, {"key": "coil/intercept/EngineInterceptor$decode$1.class", "name": "coil/intercept/EngineInterceptor$decode$1.class", "size": 2286, "crc": -1082211074}, {"key": "coil/intercept/EngineInterceptor$execute$1.class", "name": "coil/intercept/EngineInterceptor$execute$1.class", "size": 2201, "crc": 210377369}, {"key": "coil/intercept/EngineInterceptor$execute$executeResult$1.class", "name": "coil/intercept/EngineInterceptor$execute$executeResult$1.class", "size": 4929, "crc": -736339415}, {"key": "coil/intercept/EngineInterceptor$fetch$1.class", "name": "coil/intercept/EngineInterceptor$fetch$1.class", "size": 2193, "crc": 1513085748}, {"key": "coil/intercept/EngineInterceptor$intercept$1.class", "name": "coil/intercept/EngineInterceptor$intercept$1.class", "size": 1778, "crc": 371724086}, {"key": "coil/intercept/EngineInterceptor$intercept$2.class", "name": "coil/intercept/EngineInterceptor$intercept$2.class", "size": 6817, "crc": -1602855150}, {"key": "coil/intercept/EngineInterceptor$transform$3.class", "name": "coil/intercept/EngineInterceptor$transform$3.class", "size": 8047, "crc": 309051036}, {"key": "coil/intercept/EngineInterceptor.class", "name": "coil/intercept/EngineInterceptor.class", "size": 25385, "crc": -1935489223}, {"key": "coil/intercept/Interceptor$Chain.class", "name": "coil/intercept/Interceptor$Chain.class", "size": 1499, "crc": 180488576}, {"key": "coil/intercept/Interceptor.class", "name": "coil/intercept/Interceptor.class", "size": 1011, "crc": 99833555}, {"key": "coil/intercept/RealInterceptorChain$proceed$1.class", "name": "coil/intercept/RealInterceptorChain$proceed$1.class", "size": 1710, "crc": -593191298}, {"key": "coil/intercept/RealInterceptorChain.class", "name": "coil/intercept/RealInterceptorChain.class", "size": 7380, "crc": -622936093}, {"key": "coil/key/FileKeyer.class", "name": "coil/key/FileKeyer.class", "size": 1709, "crc": -677442301}, {"key": "coil/key/Keyer.class", "name": "coil/key/Keyer.class", "size": 849, "crc": -768368396}, {"key": "coil/key/UriKeyer.class", "name": "coil/key/UriKeyer.class", "size": 2071, "crc": 1035193133}, {"key": "coil/map/ByteArrayMapper.class", "name": "coil/map/ByteArrayMapper.class", "size": 1303, "crc": -151431279}, {"key": "coil/map/FileUriMapper.class", "name": "coil/map/FileUriMapper.class", "size": 2778, "crc": -1673489647}, {"key": "coil/map/HttpUrlMapper.class", "name": "coil/map/HttpUrlMapper.class", "size": 1304, "crc": 862285467}, {"key": "coil/map/Mapper.class", "name": "coil/map/Mapper.class", "size": 864, "crc": 1062248472}, {"key": "coil/map/ResourceIntMapper.class", "name": "coil/map/ResourceIntMapper.class", "size": 3219, "crc": 703092939}, {"key": "coil/map/ResourceUriMapper.class", "name": "coil/map/ResourceUriMapper.class", "size": 4126, "crc": 231017735}, {"key": "coil/map/StringMapper.class", "name": "coil/map/StringMapper.class", "size": 1952, "crc": 515085831}, {"key": "coil/memory/EmptyStrongMemoryCache.class", "name": "coil/memory/EmptyStrongMemoryCache.class", "size": 2963, "crc": -1127533466}, {"key": "coil/memory/EmptyWeakMemoryCache.class", "name": "coil/memory/EmptyWeakMemoryCache.class", "size": 2333, "crc": -1490929836}, {"key": "coil/memory/MemoryCache$Builder.class", "name": "coil/memory/MemoryCache$Builder.class", "size": 4437, "crc": -1757049324}, {"key": "coil/memory/MemoryCache$Key$Companion$CREATOR$1.class", "name": "coil/memory/MemoryCache$Key$Companion$CREATOR$1.class", "size": 2366, "crc": 1049681144}, {"key": "coil/memory/MemoryCache$Key$Companion.class", "name": "coil/memory/MemoryCache$Key$Companion.class", "size": 962, "crc": 2126255659}, {"key": "coil/memory/MemoryCache$Key.class", "name": "coil/memory/MemoryCache$Key.class", "size": 5602, "crc": -1236017094}, {"key": "coil/memory/MemoryCache$Value.class", "name": "coil/memory/MemoryCache$Value.class", "size": 3353, "crc": -460801801}, {"key": "coil/memory/MemoryCache.class", "name": "coil/memory/MemoryCache.class", "size": 1574, "crc": -683471725}, {"key": "coil/memory/MemoryCacheService$Companion.class", "name": "coil/memory/MemoryCacheService$Companion.class", "size": 1582, "crc": -1953110925}, {"key": "coil/memory/MemoryCacheService.class", "name": "coil/memory/MemoryCacheService.class", "size": 15627, "crc": -1784525177}, {"key": "coil/memory/RealMemoryCache.class", "name": "coil/memory/RealMemoryCache.class", "size": 3564, "crc": -1866611797}, {"key": "coil/memory/RealStrongMemoryCache$InternalValue.class", "name": "coil/memory/RealStrongMemoryCache$InternalValue.class", "size": 1738, "crc": -863653956}, {"key": "coil/memory/RealStrongMemoryCache$cache$1.class", "name": "coil/memory/RealStrongMemoryCache$cache$1.class", "size": 2589, "crc": -2105076439}, {"key": "coil/memory/RealStrongMemoryCache.class", "name": "coil/memory/RealStrongMemoryCache.class", "size": 4785, "crc": -1760067961}, {"key": "coil/memory/RealWeakMemoryCache$Companion.class", "name": "coil/memory/RealWeakMemoryCache$Companion.class", "size": 864, "crc": 1671959034}, {"key": "coil/memory/RealWeakMemoryCache$InternalValue.class", "name": "coil/memory/RealWeakMemoryCache$InternalValue.class", "size": 2189, "crc": 1103796584}, {"key": "coil/memory/RealWeakMemoryCache.class", "name": "coil/memory/RealWeakMemoryCache.class", "size": 8537, "crc": -1284185379}, {"key": "coil/memory/StrongMemoryCache.class", "name": "coil/memory/StrongMemoryCache.class", "size": 1778, "crc": 1457704682}, {"key": "coil/memory/WeakMemoryCache.class", "name": "coil/memory/WeakMemoryCache.class", "size": 1672, "crc": 1582230658}, {"key": "coil/network/CacheResponse.class", "name": "coil/network/CacheResponse.class", "size": 5460, "crc": 919833853}, {"key": "coil/network/CacheStrategy$Companion.class", "name": "coil/network/CacheStrategy$Companion.class", "size": 3827, "crc": 272286613}, {"key": "coil/network/CacheStrategy$Factory.class", "name": "coil/network/CacheStrategy$Factory.class", "size": 6252, "crc": -61232959}, {"key": "coil/network/CacheStrategy.class", "name": "coil/network/CacheStrategy.class", "size": 1796, "crc": 1717894058}, {"key": "coil/network/EmptyNetworkObserver.class", "name": "coil/network/EmptyNetworkObserver.class", "size": 801, "crc": 685594242}, {"key": "coil/network/HttpException.class", "name": "coil/network/HttpException.class", "size": 1299, "crc": 687198982}, {"key": "coil/network/NetworkObserver$Listener.class", "name": "coil/network/NetworkObserver$Listener.class", "size": 646, "crc": -1645005506}, {"key": "coil/network/NetworkObserver.class", "name": "coil/network/NetworkObserver.class", "size": 582, "crc": 1856115006}, {"key": "coil/network/NetworkObserverKt.class", "name": "coil/network/NetworkObserverKt.class", "size": 3718, "crc": 1505038387}, {"key": "coil/network/RealNetworkObserver$networkCallback$1.class", "name": "coil/network/RealNetworkObserver$networkCallback$1.class", "size": 1501, "crc": -1112807186}, {"key": "coil/network/RealNetworkObserver.class", "name": "coil/network/RealNetworkObserver.class", "size": 4746, "crc": -35962504}, {"key": "coil/request/BaseRequestDelegate.class", "name": "coil/request/BaseRequestDelegate.class", "size": 1965, "crc": -1968048669}, {"key": "coil/request/CachePolicy.class", "name": "coil/request/CachePolicy.class", "size": 2297, "crc": 706903676}, {"key": "coil/request/DefaultRequestOptions.class", "name": "coil/request/DefaultRequestOptions.class", "size": 9551, "crc": -1650058209}, {"key": "coil/request/DefinedRequestOptions.class", "name": "coil/request/DefinedRequestOptions.class", "size": 9006, "crc": -1755452377}, {"key": "coil/request/Disposable.class", "name": "coil/request/Disposable.class", "size": 811, "crc": -1004155616}, {"key": "coil/request/ErrorResult.class", "name": "coil/request/ErrorResult.class", "size": 3030, "crc": 1419280774}, {"key": "coil/request/GlobalLifecycle$owner$1.class", "name": "coil/request/GlobalLifecycle$owner$1.class", "size": 1048, "crc": 1248182199}, {"key": "coil/request/GlobalLifecycle.class", "name": "coil/request/GlobalLifecycle.class", "size": 2521, "crc": 1982051991}, {"key": "coil/request/ImageRequest$Builder$listener$1.class", "name": "coil/request/ImageRequest$Builder$listener$1.class", "size": 1399, "crc": 1864319328}, {"key": "coil/request/ImageRequest$Builder$listener$2.class", "name": "coil/request/ImageRequest$Builder$listener$2.class", "size": 1399, "crc": 477395060}, {"key": "coil/request/ImageRequest$Builder$listener$3.class", "name": "coil/request/ImageRequest$Builder$listener$3.class", "size": 1473, "crc": -757965951}, {"key": "coil/request/ImageRequest$Builder$listener$4.class", "name": "coil/request/ImageRequest$Builder$listener$4.class", "size": 1479, "crc": 123026913}, {"key": "coil/request/ImageRequest$Builder$listener$5.class", "name": "coil/request/ImageRequest$Builder$listener$5.class", "size": 3030, "crc": 675937890}, {"key": "coil/request/ImageRequest$Builder$target$1.class", "name": "coil/request/ImageRequest$Builder$target$1.class", "size": 1428, "crc": 1349140027}, {"key": "coil/request/ImageRequest$Builder$target$2.class", "name": "coil/request/ImageRequest$Builder$target$2.class", "size": 1428, "crc": -1738102739}, {"key": "coil/request/ImageRequest$Builder$target$3.class", "name": "coil/request/ImageRequest$Builder$target$3.class", "size": 1428, "crc": 920864577}, {"key": "coil/request/ImageRequest$Builder$target$4.class", "name": "coil/request/ImageRequest$Builder$target$4.class", "size": 2101, "crc": 339910026}, {"key": "coil/request/ImageRequest$Builder.class", "name": "coil/request/ImageRequest$Builder.class", "size": 43185, "crc": -393925706}, {"key": "coil/request/ImageRequest$Listener$DefaultImpls.class", "name": "coil/request/ImageRequest$Listener$DefaultImpls.class", "size": 1735, "crc": 21912983}, {"key": "coil/request/ImageRequest$Listener.class", "name": "coil/request/ImageRequest$Listener.class", "size": 2285, "crc": 1014267594}, {"key": "coil/request/ImageRequest.class", "name": "coil/request/ImageRequest.class", "size": 19222, "crc": 313466975}, {"key": "coil/request/ImageResult.class", "name": "coil/request/ImageResult.class", "size": 1198, "crc": -2110758361}, {"key": "coil/request/NullRequestData.class", "name": "coil/request/NullRequestData.class", "size": 843, "crc": -1959661130}, {"key": "coil/request/NullRequestDataException.class", "name": "coil/request/NullRequestDataException.class", "size": 675, "crc": -656334078}, {"key": "coil/request/OneShotDisposable.class", "name": "coil/request/OneShotDisposable.class", "size": 1855, "crc": 461194982}, {"key": "coil/request/Options.class", "name": "coil/request/Options.class", "size": 8681, "crc": 379710911}, {"key": "coil/request/Parameters$Builder.class", "name": "coil/request/Parameters$Builder.class", "size": 3309, "crc": -1972396713}, {"key": "coil/request/Parameters$Companion.class", "name": "coil/request/Parameters$Companion.class", "size": 853, "crc": 1716939514}, {"key": "coil/request/Parameters$Entry.class", "name": "coil/request/Parameters$Entry.class", "size": 2262, "crc": -1062212984}, {"key": "coil/request/Parameters.class", "name": "coil/request/Parameters.class", "size": 8302, "crc": 1401213104}, {"key": "coil/request/RequestDelegate.class", "name": "coil/request/RequestDelegate.class", "size": 1086, "crc": 672313445}, {"key": "coil/request/RequestService.class", "name": "coil/request/RequestService.class", "size": 8883, "crc": 1675558804}, {"key": "coil/request/SuccessResult.class", "name": "coil/request/SuccessResult.class", "size": 4997, "crc": -87009497}, {"key": "coil/request/Tags$Companion.class", "name": "coil/request/Tags$Companion.class", "size": 1487, "crc": 1998022059}, {"key": "coil/request/Tags.class", "name": "coil/request/Tags.class", "size": 3554, "crc": 632074194}, {"key": "coil/request/ViewTargetDisposable.class", "name": "coil/request/ViewTargetDisposable.class", "size": 2147, "crc": 110009815}, {"key": "coil/request/ViewTargetRequestDelegate.class", "name": "coil/request/ViewTargetRequestDelegate.class", "size": 3712, "crc": -1057477826}, {"key": "coil/request/ViewTargetRequestManager$dispose$1.class", "name": "coil/request/ViewTargetRequestManager$dispose$1.class", "size": 3074, "crc": 578912799}, {"key": "coil/request/ViewTargetRequestManager.class", "name": "coil/request/ViewTargetRequestManager.class", "size": 5192, "crc": -1494251622}, {"key": "coil/size/-Dimensions.class", "name": "coil/size/-Dimensions.class", "size": 1510, "crc": 157243865}, {"key": "coil/size/-Sizes.class", "name": "coil/size/-Sizes.class", "size": 2159, "crc": 976814513}, {"key": "coil/size/Dimension$Pixels.class", "name": "coil/size/Dimension$Pixels.class", "size": 2125, "crc": -903906602}, {"key": "coil/size/Dimension$Undefined.class", "name": "coil/size/Dimension$Undefined.class", "size": 957, "crc": -478444000}, {"key": "coil/size/Dimension.class", "name": "coil/size/Dimension.class", "size": 905, "crc": -1190967138}, {"key": "coil/size/DisplaySizeResolver.class", "name": "coil/size/DisplaySizeResolver.class", "size": 2452, "crc": -227988879}, {"key": "coil/size/Precision.class", "name": "coil/size/Precision.class", "size": 1787, "crc": 304622853}, {"key": "coil/size/RealSizeResolver.class", "name": "coil/size/RealSizeResolver.class", "size": 1668, "crc": 1487598257}, {"key": "coil/size/RealViewSizeResolver.class", "name": "coil/size/RealViewSizeResolver.class", "size": 1986, "crc": 798049092}, {"key": "coil/size/Scale.class", "name": "coil/size/Scale.class", "size": 1687, "crc": 2123850535}, {"key": "coil/size/Size$Companion.class", "name": "coil/size/Size$Companion.class", "size": 814, "crc": 782795154}, {"key": "coil/size/Size.class", "name": "coil/size/Size.class", "size": 3083, "crc": 1215893633}, {"key": "coil/size/SizeResolver.class", "name": "coil/size/SizeResolver.class", "size": 795, "crc": 1849882189}, {"key": "coil/size/SizeResolvers.class", "name": "coil/size/SizeResolvers.class", "size": 882, "crc": 886340711}, {"key": "coil/size/ViewSizeResolver$DefaultImpls.class", "name": "coil/size/ViewSizeResolver$DefaultImpls.class", "size": 1294, "crc": 632475234}, {"key": "coil/size/ViewSizeResolver$size$3$1.class", "name": "coil/size/ViewSizeResolver$size$3$1.class", "size": 2061, "crc": -346325221}, {"key": "coil/size/ViewSizeResolver$size$3$preDrawListener$1.class", "name": "coil/size/ViewSizeResolver$size$3$preDrawListener$1.class", "size": 2344, "crc": 75054078}, {"key": "coil/size/ViewSizeResolver.class", "name": "coil/size/ViewSizeResolver.class", "size": 7656, "crc": -993127789}, {"key": "coil/size/ViewSizeResolvers.class", "name": "coil/size/ViewSizeResolvers.class", "size": 1553, "crc": 1212936443}, {"key": "coil/target/GenericViewTarget.class", "name": "coil/target/GenericViewTarget.class", "size": 2838, "crc": -1398300792}, {"key": "coil/target/ImageViewTarget.class", "name": "coil/target/ImageViewTarget.class", "size": 2150, "crc": 1091915719}, {"key": "coil/target/Target$DefaultImpls.class", "name": "coil/target/Target$DefaultImpls.class", "size": 1231, "crc": -487706940}, {"key": "coil/target/Target.class", "name": "coil/target/Target.class", "size": 1561, "crc": -643075449}, {"key": "coil/target/ViewTarget$DefaultImpls.class", "name": "coil/target/ViewTarget$DefaultImpls.class", "size": 1381, "crc": -986397761}, {"key": "coil/target/ViewTarget.class", "name": "coil/target/ViewTarget.class", "size": 1377, "crc": -1540893940}, {"key": "coil/transform/CircleCropTransformation.class", "name": "coil/transform/CircleCropTransformation.class", "size": 4288, "crc": -1582934022}, {"key": "coil/transform/RoundedCornersTransformation.class", "name": "coil/transform/RoundedCornersTransformation.class", "size": 9433, "crc": -285991150}, {"key": "coil/transform/Transformation.class", "name": "coil/transform/Transformation.class", "size": 1062, "crc": 597761235}, {"key": "coil/transition/CrossfadeTransition$Factory.class", "name": "coil/transition/CrossfadeTransition$Factory.class", "size": 3632, "crc": -439915717}, {"key": "coil/transition/CrossfadeTransition.class", "name": "coil/transition/CrossfadeTransition.class", "size": 4102, "crc": 1244458230}, {"key": "coil/transition/NoneTransition$Factory.class", "name": "coil/transition/NoneTransition$Factory.class", "size": 1726, "crc": 594306273}, {"key": "coil/transition/NoneTransition.class", "name": "coil/transition/NoneTransition.class", "size": 1675, "crc": -737567370}, {"key": "coil/transition/Transition$Factory$Companion.class", "name": "coil/transition/Transition$Factory$Companion.class", "size": 847, "crc": -13209633}, {"key": "coil/transition/Transition$Factory.class", "name": "coil/transition/Transition$Factory.class", "size": 1343, "crc": -998198867}, {"key": "coil/transition/Transition.class", "name": "coil/transition/Transition.class", "size": 588, "crc": 738353037}, {"key": "coil/transition/TransitionTarget$DefaultImpls.class", "name": "coil/transition/TransitionTarget$DefaultImpls.class", "size": 1297, "crc": -1245055969}, {"key": "coil/transition/TransitionTarget.class", "name": "coil/transition/TransitionTarget.class", "size": 1473, "crc": 523542997}, {"key": "coil/util/-Bitmaps.class", "name": "coil/util/-Bitmaps.class", "size": 4663, "crc": -1080709734}, {"key": "coil/util/-Calls.class", "name": "coil/util/-Calls.class", "size": 3033, "crc": -270819550}, {"key": "coil/util/-Collections.class", "name": "coil/util/-Collections.class", "size": 7151, "crc": 1375742174}, {"key": "coil/util/-Contexts.class", "name": "coil/util/-Contexts.class", "size": 6354, "crc": 1598088849}, {"key": "coil/util/-FileSystems.class", "name": "coil/util/-FileSystems.class", "size": 2002, "crc": 1341260448}, {"key": "coil/util/-HardwareBitmaps.class", "name": "coil/util/-HardwareBitmaps.class", "size": 4707, "crc": 567303745}, {"key": "coil/util/-Lifecycles$awaitStarted$1.class", "name": "coil/util/-Lifecycles$awaitStarted$1.class", "size": 1473, "crc": 1435053032}, {"key": "coil/util/-Lifecycles$awaitStarted$2$1.class", "name": "coil/util/-Lifecycles$awaitStarted$2$1.class", "size": 1617, "crc": -498784457}, {"key": "coil/util/-Lifecycles.class", "name": "coil/util/-Lifecycles.class", "size": 5297, "crc": -1707385482}, {"key": "coil/util/-Logs.class", "name": "coil/util/-Logs.class", "size": 1638, "crc": 1600666233}, {"key": "coil/util/-Requests$WhenMappings.class", "name": "coil/util/-Requests$WhenMappings.class", "size": 746, "crc": 465719751}, {"key": "coil/util/-Requests.class", "name": "coil/util/-Requests.class", "size": 3776, "crc": -2067044404}, {"key": "coil/util/-Utils$WhenMappings.class", "name": "coil/util/-Utils$WhenMappings.class", "size": 1449, "crc": 362168672}, {"key": "coil/util/-Utils.class", "name": "coil/util/-Utils.class", "size": 21095, "crc": -1975202153}, {"key": "coil/util/CoilUtils.class", "name": "coil/util/CoilUtils.class", "size": 2013, "crc": -282369369}, {"key": "coil/util/ContinuationCallback.class", "name": "coil/util/ContinuationCallback.class", "size": 2894, "crc": -384462154}, {"key": "coil/util/DebugLogger.class", "name": "coil/util/DebugLogger.class", "size": 3002, "crc": 1839250140}, {"key": "coil/util/DrawableUtils.class", "name": "coil/util/DrawableUtils.class", "size": 6697, "crc": 584314261}, {"key": "coil/util/Emoji.class", "name": "coil/util/Emoji.class", "size": 1052, "crc": -2138628741}, {"key": "coil/util/FileDescriptorCounter.class", "name": "coil/util/FileDescriptorCounter.class", "size": 3892, "crc": -1490347637}, {"key": "coil/util/HardwareBitmapService.class", "name": "coil/util/HardwareBitmapService.class", "size": 906, "crc": -981783894}, {"key": "coil/util/ImageLoaderOptions.class", "name": "coil/util/ImageLoaderOptions.class", "size": 2896, "crc": -549431239}, {"key": "coil/util/ImmutableHardwareBitmapService.class", "name": "coil/util/ImmutableHardwareBitmapService.class", "size": 1091, "crc": -185020676}, {"key": "coil/util/LimitedFileDescriptorHardwareBitmapService$Companion.class", "name": "coil/util/LimitedFileDescriptorHardwareBitmapService$Companion.class", "size": 928, "crc": -1653911613}, {"key": "coil/util/LimitedFileDescriptorHardwareBitmapService.class", "name": "coil/util/LimitedFileDescriptorHardwareBitmapService.class", "size": 3132, "crc": 1612865472}, {"key": "coil/util/Logger.class", "name": "coil/util/Logger.class", "size": 810, "crc": -580000457}, {"key": "coil/util/SingletonDiskCache.class", "name": "coil/util/SingletonDiskCache.class", "size": 2259, "crc": **********}, {"key": "coil/util/SystemCallbacks$Companion.class", "name": "coil/util/SystemCallbacks$Companion.class", "size": 896, "crc": -213559451}, {"key": "coil/util/SystemCallbacks.class", "name": "coil/util/SystemCallbacks.class", "size": 8185, "crc": -980396494}, {"key": "coil/util/Time$provider$1.class", "name": "coil/util/Time$provider$1.class", "size": 1133, "crc": -**********}, {"key": "coil/util/Time$reset$1.class", "name": "coil/util/Time$reset$1.class", "size": 1140, "crc": -223894899}, {"key": "coil/util/Time.class", "name": "coil/util/Time.class", "size": 2020, "crc": -**********}, {"key": "META-INF/coil-base_release.kotlin_module", "name": "META-INF/coil-base_release.kotlin_module", "size": 318, "crc": -943424102}]