[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 57, "crc": -1355821187}, {"key": "okhttp3/CipherSuite.class", "name": "okhttp3/CipherSuite.class", "size": 14349, "crc": 1103520636}, {"key": "okhttp3/Headers$Builder.class", "name": "okhttp3/Headers$Builder.class", "size": 8765, "crc": -1709422917}, {"key": "okhttp3/Interceptor.class", "name": "okhttp3/Interceptor.class", "size": 965, "crc": 6919886}, {"key": "okhttp3/CertificatePinner.class", "name": "okhttp3/CertificatePinner.class", "size": 9446, "crc": 110397001}, {"key": "okhttp3/MultipartReader.class", "name": "okhttp3/MultipartReader.class", "size": 6728, "crc": 513148745}, {"key": "okhttp3/EventListener$Companion.class", "name": "okhttp3/EventListener$Companion.class", "size": 831, "crc": 1307306461}, {"key": "okhttp3/HttpUrl$Builder$Companion.class", "name": "okhttp3/HttpUrl$Builder$Companion.class", "size": 3476, "crc": 63659496}, {"key": "okhttp3/Cache$RealCacheRequest$1.class", "name": "okhttp3/Cache$RealCacheRequest$1.class", "size": 1850, "crc": -1196931196}, {"key": "okhttp3/internal/authenticator/JavaNetAuthenticator$WhenMappings.class", "name": "okhttp3/internal/authenticator/JavaNetAuthenticator$WhenMappings.class", "size": 767, "crc": 1787819013}, {"key": "okhttp3/internal/authenticator/JavaNetAuthenticator.class", "name": "okhttp3/internal/authenticator/JavaNetAuthenticator.class", "size": 5630, "crc": -1093785831}, {"key": "okhttp3/internal/proxy/NullProxySelector.class", "name": "okhttp3/internal/proxy/NullProxySelector.class", "size": 2355, "crc": -128898171}, {"key": "okhttp3/internal/http1/HeadersReader$Companion.class", "name": "okhttp3/internal/http1/HeadersReader$Companion.class", "size": 856, "crc": -1580907140}, {"key": "okhttp3/internal/http1/Http1ExchangeCodec$ChunkedSource.class", "name": "okhttp3/internal/http1/Http1ExchangeCodec$ChunkedSource.class", "size": 5822, "crc": 1557368498}, {"key": "okhttp3/internal/http1/HeadersReader.class", "name": "okhttp3/internal/http1/HeadersReader.class", "size": 2312, "crc": -2001272459}, {"key": "okhttp3/internal/http1/Http1ExchangeCodec$AbstractSource.class", "name": "okhttp3/internal/http1/Http1ExchangeCodec$AbstractSource.class", "size": 3238, "crc": -1152512172}, {"key": "okhttp3/internal/http1/Http1ExchangeCodec$ChunkedSink.class", "name": "okhttp3/internal/http1/Http1ExchangeCodec$ChunkedSink.class", "size": 3277, "crc": -1024405968}, {"key": "okhttp3/internal/http1/Http1ExchangeCodec.class", "name": "okhttp3/internal/http1/Http1ExchangeCodec.class", "size": 14318, "crc": 17546557}, {"key": "okhttp3/internal/http1/Http1ExchangeCodec$FixedLengthSource.class", "name": "okhttp3/internal/http1/Http1ExchangeCodec$FixedLengthSource.class", "size": 3714, "crc": -1622237555}, {"key": "okhttp3/internal/http1/Http1ExchangeCodec$Companion.class", "name": "okhttp3/internal/http1/Http1ExchangeCodec$Companion.class", "size": 1211, "crc": -1195683678}, {"key": "okhttp3/internal/http1/Http1ExchangeCodec$UnknownLengthSource.class", "name": "okhttp3/internal/http1/Http1ExchangeCodec$UnknownLengthSource.class", "size": 2970, "crc": 1347288295}, {"key": "okhttp3/internal/http1/Http1ExchangeCodec$KnownLengthSink.class", "name": "okhttp3/internal/http1/Http1ExchangeCodec$KnownLengthSink.class", "size": 3186, "crc": -264979979}, {"key": "okhttp3/internal/tls/CertificateChainCleaner.class", "name": "okhttp3/internal/tls/CertificateChainCleaner.class", "size": 1402, "crc": -1951532109}, {"key": "okhttp3/internal/tls/BasicCertificateChainCleaner$Companion.class", "name": "okhttp3/internal/tls/BasicCertificateChainCleaner$Companion.class", "size": 909, "crc": -1434328709}, {"key": "okhttp3/internal/tls/OkHostnameVerifier.class", "name": "okhttp3/internal/tls/OkHostnameVerifier.class", "size": 8161, "crc": 208581025}, {"key": "okhttp3/internal/tls/BasicCertificateChainCleaner.class", "name": "okhttp3/internal/tls/BasicCertificateChainCleaner.class", "size": 4992, "crc": 1188747979}, {"key": "okhttp3/internal/tls/BasicTrustRootIndex.class", "name": "okhttp3/internal/tls/BasicTrustRootIndex.class", "size": 4774, "crc": 579436333}, {"key": "okhttp3/internal/tls/TrustRootIndex.class", "name": "okhttp3/internal/tls/TrustRootIndex.class", "size": 727, "crc": 134016601}, {"key": "okhttp3/internal/tls/CertificateChainCleaner$Companion.class", "name": "okhttp3/internal/tls/CertificateChainCleaner$Companion.class", "size": 2353, "crc": -1920901709}, {"key": "okhttp3/internal/SuppressSignatureCheck.class", "name": "okhttp3/internal/SuppressSignatureCheck.class", "size": 900, "crc": 1733509293}, {"key": "okhttp3/internal/connection/RealCall$AsyncCall.class", "name": "okhttp3/internal/connection/RealCall$AsyncCall.class", "size": 7246, "crc": -797974279}, {"key": "okhttp3/internal/connection/RouteException.class", "name": "okhttp3/internal/connection/RouteException.class", "size": 1691, "crc": -546948123}, {"key": "okhttp3/internal/connection/RealCall$timeout$1.class", "name": "okhttp3/internal/connection/RealCall$timeout$1.class", "size": 953, "crc": 791027502}, {"key": "okhttp3/internal/connection/RealConnection$newWebSocketStreams$1.class", "name": "okhttp3/internal/connection/RealConnection$newWebSocketStreams$1.class", "size": 1451, "crc": -1717561011}, {"key": "okhttp3/internal/connection/RealCall$CallReference.class", "name": "okhttp3/internal/connection/RealCall$CallReference.class", "size": 1474, "crc": 245366816}, {"key": "okhttp3/internal/connection/RealConnection$connectTls$2.class", "name": "okhttp3/internal/connection/RealConnection$connectTls$2.class", "size": 3569, "crc": 837774435}, {"key": "okhttp3/internal/connection/Exchange$RequestBodySink.class", "name": "okhttp3/internal/connection/Exchange$RequestBodySink.class", "size": 3648, "crc": 1363892975}, {"key": "okhttp3/internal/connection/RouteSelector.class", "name": "okhttp3/internal/connection/RouteSelector.class", "size": 8752, "crc": 1379878722}, {"key": "okhttp3/internal/connection/ExchangeFinder.class", "name": "okhttp3/internal/connection/ExchangeFinder.class", "size": 10183, "crc": 1535805765}, {"key": "okhttp3/internal/connection/RealConnection$WhenMappings.class", "name": "okhttp3/internal/connection/RealConnection$WhenMappings.class", "size": 787, "crc": -1460449799}, {"key": "okhttp3/internal/connection/RealConnection$Companion.class", "name": "okhttp3/internal/connection/RealConnection$Companion.class", "size": 2082, "crc": 1123127425}, {"key": "okhttp3/internal/connection/RealConnectionPool.class", "name": "okhttp3/internal/connection/RealConnectionPool.class", "size": 12046, "crc": 1829969854}, {"key": "okhttp3/internal/connection/RealCall.class", "name": "okhttp3/internal/connection/RealCall.class", "size": 21890, "crc": -1616746212}, {"key": "okhttp3/internal/connection/RealConnectionPool$cleanupTask$1.class", "name": "okhttp3/internal/connection/RealConnectionPool$cleanupTask$1.class", "size": 1280, "crc": -330598740}, {"key": "okhttp3/internal/connection/ConnectionSpecSelector.class", "name": "okhttp3/internal/connection/ConnectionSpecSelector.class", "size": 3678, "crc": 1380701243}, {"key": "okhttp3/internal/connection/RouteSelector$Companion.class", "name": "okhttp3/internal/connection/RouteSelector$Companion.class", "size": 1703, "crc": -1033950344}, {"key": "okhttp3/internal/connection/Exchange.class", "name": "okhttp3/internal/connection/Exchange.class", "size": 9620, "crc": -1999055926}, {"key": "okhttp3/internal/connection/RouteDatabase.class", "name": "okhttp3/internal/connection/RouteDatabase.class", "size": 1570, "crc": -363891863}, {"key": "okhttp3/internal/connection/Exchange$ResponseBodySource.class", "name": "okhttp3/internal/connection/Exchange$ResponseBodySource.class", "size": 4062, "crc": -1425239895}, {"key": "okhttp3/internal/connection/ConnectInterceptor.class", "name": "okhttp3/internal/connection/ConnectInterceptor.class", "size": 2091, "crc": 1861838497}, {"key": "okhttp3/internal/connection/RealConnection.class", "name": "okhttp3/internal/connection/RealConnection.class", "size": 32370, "crc": 179923344}, {"key": "okhttp3/internal/connection/RealConnection$connectTls$1.class", "name": "okhttp3/internal/connection/RealConnection$connectTls$1.class", "size": 2121, "crc": 814768000}, {"key": "okhttp3/internal/connection/RealConnectionPool$Companion.class", "name": "okhttp3/internal/connection/RealConnectionPool$Companion.class", "size": 1500, "crc": -1301840099}, {"key": "okhttp3/internal/connection/RouteSelector$Selection.class", "name": "okhttp3/internal/connection/RouteSelector$Selection.class", "size": 1845, "crc": 1519270899}, {"key": "okhttp3/internal/publicsuffix/PublicSuffixDatabase$Companion.class", "name": "okhttp3/internal/publicsuffix/PublicSuffixDatabase$Companion.class", "size": 3335, "crc": -1825365899}, {"key": "okhttp3/internal/publicsuffix/PublicSuffixDatabase.class", "name": "okhttp3/internal/publicsuffix/PublicSuffixDatabase.class", "size": 9881, "crc": -880948810}, {"key": "okhttp3/internal/cache/DiskLruCache$Entry$newSource$1.class", "name": "okhttp3/internal/cache/DiskLruCache$Entry$newSource$1.class", "size": 1861, "crc": 1216740451}, {"key": "okhttp3/internal/cache/DiskLruCache$Companion.class", "name": "okhttp3/internal/cache/DiskLruCache$Companion.class", "size": 1274, "crc": 1811271350}, {"key": "okhttp3/internal/cache/CacheInterceptor$Companion.class", "name": "okhttp3/internal/cache/CacheInterceptor$Companion.class", "size": 3695, "crc": 1594399350}, {"key": "okhttp3/internal/cache/DiskLruCache$newJournalWriter$faultHidingSink$1.class", "name": "okhttp3/internal/cache/DiskLruCache$newJournalWriter$faultHidingSink$1.class", "size": 3201, "crc": -710027566}, {"key": "okhttp3/internal/cache/DiskLruCache$snapshots$1.class", "name": "okhttp3/internal/cache/DiskLruCache$snapshots$1.class", "size": 4247, "crc": -2131432579}, {"key": "okhttp3/internal/cache/CacheStrategy.class", "name": "okhttp3/internal/cache/CacheStrategy.class", "size": 1597, "crc": 2010495688}, {"key": "okhttp3/internal/cache/DiskLruCache$Editor$newSink$1$1.class", "name": "okhttp3/internal/cache/DiskLruCache$Editor$newSink$1$1.class", "size": 2025, "crc": -932664729}, {"key": "okhttp3/internal/cache/CacheStrategy$Factory.class", "name": "okhttp3/internal/cache/CacheStrategy$Factory.class", "size": 7559, "crc": 1305982959}, {"key": "okhttp3/internal/cache/DiskLruCache$Entry.class", "name": "okhttp3/internal/cache/DiskLruCache$Entry.class", "size": 9158, "crc": -340432770}, {"key": "okhttp3/internal/cache/CacheRequest.class", "name": "okhttp3/internal/cache/CacheRequest.class", "size": 600, "crc": 722861358}, {"key": "okhttp3/internal/cache/DiskLruCache$Editor.class", "name": "okhttp3/internal/cache/DiskLruCache$Editor.class", "size": 5528, "crc": -281859948}, {"key": "okhttp3/internal/cache/CacheInterceptor$cacheWritingResponse$cacheWritingSource$1.class", "name": "okhttp3/internal/cache/CacheInterceptor$cacheWritingResponse$cacheWritingSource$1.class", "size": 2856, "crc": -1718886165}, {"key": "okhttp3/internal/cache/DiskLruCache$cleanupTask$1.class", "name": "okhttp3/internal/cache/DiskLruCache$cleanupTask$1.class", "size": 2275, "crc": 981254961}, {"key": "okhttp3/internal/cache/FaultHidingSink.class", "name": "okhttp3/internal/cache/FaultHidingSink.class", "size": 2502, "crc": 124265005}, {"key": "okhttp3/internal/cache/CacheInterceptor.class", "name": "okhttp3/internal/cache/CacheInterceptor.class", "size": 8293, "crc": -207417722}, {"key": "okhttp3/internal/cache/DiskLruCache.class", "name": "okhttp3/internal/cache/DiskLruCache.class", "size": 23976, "crc": -1737914696}, {"key": "okhttp3/internal/cache/CacheStrategy$Companion.class", "name": "okhttp3/internal/cache/CacheStrategy$Companion.class", "size": 1928, "crc": 1093348023}, {"key": "okhttp3/internal/cache/DiskLruCache$Snapshot.class", "name": "okhttp3/internal/cache/DiskLruCache$Snapshot.class", "size": 3007, "crc": 1387222850}, {"key": "okhttp3/internal/ws/RealWebSocket$WriterTask.class", "name": "okhttp3/internal/ws/RealWebSocket$WriterTask.class", "size": 1539, "crc": 9589848}, {"key": "okhttp3/internal/ws/RealWebSocket$connect$1.class", "name": "okhttp3/internal/ws/RealWebSocket$connect$1.class", "size": 4576, "crc": 870358654}, {"key": "okhttp3/internal/ws/MessageInflater.class", "name": "okhttp3/internal/ws/MessageInflater.class", "size": 2268, "crc": -570389824}, {"key": "okhttp3/internal/ws/MessageDeflaterKt.class", "name": "okhttp3/internal/ws/MessageDeflaterKt.class", "size": 1000, "crc": -258307322}, {"key": "okhttp3/internal/ws/MessageInflaterKt.class", "name": "okhttp3/internal/ws/MessageInflaterKt.class", "size": 412, "crc": 1069692720}, {"key": "okhttp3/internal/ws/WebSocketReader.class", "name": "okhttp3/internal/ws/WebSocketReader.class", "size": 8338, "crc": -1291334169}, {"key": "okhttp3/internal/ws/WebSocketReader$FrameCallback.class", "name": "okhttp3/internal/ws/WebSocketReader$FrameCallback.class", "size": 1076, "crc": -1206892644}, {"key": "okhttp3/internal/ws/RealWebSocket.class", "name": "okhttp3/internal/ws/RealWebSocket.class", "size": 24078, "crc": 1108796751}, {"key": "okhttp3/internal/ws/WebSocketWriter.class", "name": "okhttp3/internal/ws/WebSocketWriter.class", "size": 6714, "crc": 1089451424}, {"key": "okhttp3/internal/ws/RealWebSocket$initReaderAndWriter$lambda$3$$inlined$schedule$1.class", "name": "okhttp3/internal/ws/RealWebSocket$initReaderAndWriter$lambda$3$$inlined$schedule$1.class", "size": 2042, "crc": -407215341}, {"key": "okhttp3/internal/ws/WebSocketProtocol.class", "name": "okhttp3/internal/ws/WebSocketProtocol.class", "size": 4992, "crc": 1170548191}, {"key": "okhttp3/internal/ws/WebSocketExtensions$Companion.class", "name": "okhttp3/internal/ws/WebSocketExtensions$Companion.class", "size": 3601, "crc": 802895198}, {"key": "okhttp3/internal/ws/RealWebSocket$writeOneFrame$lambda$8$$inlined$execute$default$1.class", "name": "okhttp3/internal/ws/RealWebSocket$writeOneFrame$lambda$8$$inlined$execute$default$1.class", "size": 1904, "crc": 6046407}, {"key": "okhttp3/internal/ws/RealWebSocket$Close.class", "name": "okhttp3/internal/ws/RealWebSocket$Close.class", "size": 1371, "crc": 629338424}, {"key": "okhttp3/internal/ws/RealWebSocket$Streams.class", "name": "okhttp3/internal/ws/RealWebSocket$Streams.class", "size": 1609, "crc": 2022553948}, {"key": "okhttp3/internal/ws/MessageDeflater.class", "name": "okhttp3/internal/ws/MessageDeflater.class", "size": 3284, "crc": 855558921}, {"key": "okhttp3/internal/ws/RealWebSocket$Companion.class", "name": "okhttp3/internal/ws/RealWebSocket$Companion.class", "size": 1032, "crc": 320407924}, {"key": "okhttp3/internal/ws/WebSocketExtensions.class", "name": "okhttp3/internal/ws/WebSocketExtensions.class", "size": 4958, "crc": 1706010014}, {"key": "okhttp3/internal/ws/RealWebSocket$Message.class", "name": "okhttp3/internal/ws/RealWebSocket$Message.class", "size": 1293, "crc": 1481167373}, {"key": "okhttp3/internal/Internal.class", "name": "okhttp3/internal/Internal.class", "size": 3173, "crc": -2079614601}, {"key": "okhttp3/internal/platform/OpenJSSEPlatform$Companion.class", "name": "okhttp3/internal/platform/OpenJSSEPlatform$Companion.class", "size": 1326, "crc": 1981791839}, {"key": "okhttp3/internal/platform/Android10Platform.class", "name": "okhttp3/internal/platform/Android10Platform.class", "size": 8197, "crc": 985345250}, {"key": "okhttp3/internal/platform/Android10Platform$Companion.class", "name": "okhttp3/internal/platform/Android10Platform$Companion.class", "size": 1351, "crc": 1893328099}, {"key": "okhttp3/internal/platform/Platform$Companion.class", "name": "okhttp3/internal/platform/Platform$Companion.class", "size": 8787, "crc": 1466930467}, {"key": "okhttp3/internal/platform/BouncyCastlePlatform.class", "name": "okhttp3/internal/platform/BouncyCastlePlatform.class", "size": 6982, "crc": 758946240}, {"key": "okhttp3/internal/platform/ConscryptPlatform$Companion.class", "name": "okhttp3/internal/platform/ConscryptPlatform$Companion.class", "size": 2078, "crc": -1370600981}, {"key": "okhttp3/internal/platform/ConscryptPlatform.class", "name": "okhttp3/internal/platform/ConscryptPlatform.class", "size": 7821, "crc": -976047419}, {"key": "okhttp3/internal/platform/ConscryptPlatform$DisabledHostnameVerifier.class", "name": "okhttp3/internal/platform/ConscryptPlatform$DisabledHostnameVerifier.class", "size": 1681, "crc": -**********}, {"key": "okhttp3/internal/platform/AndroidPlatform.class", "name": "okhttp3/internal/platform/AndroidPlatform.class", "size": 11823, "crc": -706724553}, {"key": "okhttp3/internal/platform/Jdk8WithJettyBootPlatform$AlpnProvider.class", "name": "okhttp3/internal/platform/Jdk8WithJettyBootPlatform$AlpnProvider.class", "size": 3898, "crc": 531009656}, {"key": "okhttp3/internal/platform/Jdk9Platform.class", "name": "okhttp3/internal/platform/Jdk9Platform.class", "size": 5146, "crc": **********}, {"key": "okhttp3/internal/platform/Jdk9Platform$Companion.class", "name": "okhttp3/internal/platform/Jdk9Platform$Companion.class", "size": 1296, "crc": -904933968}, {"key": "okhttp3/internal/platform/OpenJSSEPlatform.class", "name": "okhttp3/internal/platform/OpenJSSEPlatform.class", "size": 6886, "crc": 487458740}, {"key": "okhttp3/internal/platform/Jdk8WithJettyBootPlatform.class", "name": "okhttp3/internal/platform/Jdk8WithJettyBootPlatform.class", "size": 5513, "crc": 748902525}, {"key": "okhttp3/internal/platform/Platform.class", "name": "okhttp3/internal/platform/Platform.class", "size": 10392, "crc": -422816887}, {"key": "okhttp3/internal/platform/android/Android10SocketAdapter$Companion.class", "name": "okhttp3/internal/platform/android/Android10SocketAdapter$Companion.class", "size": 1742, "crc": -30967431}, {"key": "okhttp3/internal/platform/android/BouncyCastleSocketAdapter$Companion.class", "name": "okhttp3/internal/platform/android/BouncyCastleSocketAdapter$Companion.class", "size": 1413, "crc": -1749220651}, {"key": "okhttp3/internal/platform/android/BouncyCastleSocketAdapter.class", "name": "okhttp3/internal/platform/android/BouncyCastleSocketAdapter.class", "size": 5816, "crc": -25227996}, {"key": "okhttp3/internal/platform/android/SocketAdapter.class", "name": "okhttp3/internal/platform/android/SocketAdapter.class", "size": 1679, "crc": 1061628076}, {"key": "okhttp3/internal/platform/android/SocketAdapter$DefaultImpls.class", "name": "okhttp3/internal/platform/android/SocketAdapter$DefaultImpls.class", "size": 1236, "crc": -1532517229}, {"key": "okhttp3/internal/platform/android/AndroidSocketAdapter.class", "name": "okhttp3/internal/platform/android/AndroidSocketAdapter.class", "size": 6491, "crc": 1021633436}, {"key": "okhttp3/internal/platform/android/AndroidCertificateChainCleaner$Companion.class", "name": "okhttp3/internal/platform/android/AndroidCertificateChainCleaner$Companion.class", "size": 2131, "crc": -1654628502}, {"key": "okhttp3/internal/platform/android/BouncyCastleSocketAdapter$Companion$factory$1.class", "name": "okhttp3/internal/platform/android/BouncyCastleSocketAdapter$Companion$factory$1.class", "size": 2041, "crc": 1171760333}, {"key": "okhttp3/internal/platform/android/AndroidLog.class", "name": "okhttp3/internal/platform/android/AndroidLog.class", "size": 5932, "crc": 1761404019}, {"key": "okhttp3/internal/platform/android/AndroidCertificateChainCleaner.class", "name": "okhttp3/internal/platform/android/AndroidCertificateChainCleaner.class", "size": 4945, "crc": 700781507}, {"key": "okhttp3/internal/platform/android/ConscryptSocketAdapter$Companion$factory$1.class", "name": "okhttp3/internal/platform/android/ConscryptSocketAdapter$Companion$factory$1.class", "size": 2034, "crc": 462162045}, {"key": "okhttp3/internal/platform/android/AndroidLogHandler.class", "name": "okhttp3/internal/platform/android/AndroidLogHandler.class", "size": 1870, "crc": -1485433517}, {"key": "okhttp3/internal/platform/android/AndroidLogKt.class", "name": "okhttp3/internal/platform/android/AndroidLogKt.class", "size": 968, "crc": 1650624706}, {"key": "okhttp3/internal/platform/android/StandardAndroidSocketAdapter.class", "name": "okhttp3/internal/platform/android/StandardAndroidSocketAdapter.class", "size": 3053, "crc": -1045125216}, {"key": "okhttp3/internal/platform/android/CloseGuard$Companion.class", "name": "okhttp3/internal/platform/android/CloseGuard$Companion.class", "size": 1848, "crc": -640378734}, {"key": "okhttp3/internal/platform/android/StandardAndroidSocketAdapter$Companion.class", "name": "okhttp3/internal/platform/android/StandardAndroidSocketAdapter$Companion.class", "size": 3175, "crc": 1865143375}, {"key": "okhttp3/internal/platform/android/ConscryptSocketAdapter$Companion.class", "name": "okhttp3/internal/platform/android/ConscryptSocketAdapter$Companion.class", "size": 1401, "crc": 1024792807}, {"key": "okhttp3/internal/platform/android/Android10SocketAdapter.class", "name": "okhttp3/internal/platform/android/Android10SocketAdapter.class", "size": 5466, "crc": 1180363220}, {"key": "okhttp3/internal/platform/android/ConscryptSocketAdapter.class", "name": "okhttp3/internal/platform/android/ConscryptSocketAdapter.class", "size": 5351, "crc": 1000688701}, {"key": "okhttp3/internal/platform/android/CloseGuard.class", "name": "okhttp3/internal/platform/android/CloseGuard.class", "size": 2373, "crc": 905331978}, {"key": "okhttp3/internal/platform/android/AndroidSocketAdapter$Companion.class", "name": "okhttp3/internal/platform/android/AndroidSocketAdapter$Companion.class", "size": 3411, "crc": -1567055181}, {"key": "okhttp3/internal/platform/android/DeferredSocketAdapter.class", "name": "okhttp3/internal/platform/android/DeferredSocketAdapter.class", "size": 3484, "crc": -1739794632}, {"key": "okhttp3/internal/platform/android/AndroidSocketAdapter$Companion$factory$1.class", "name": "okhttp3/internal/platform/android/AndroidSocketAdapter$Companion$factory$1.class", "size": 2732, "crc": -1558844330}, {"key": "okhttp3/internal/platform/android/DeferredSocketAdapter$Factory.class", "name": "okhttp3/internal/platform/android/DeferredSocketAdapter$Factory.class", "size": 989, "crc": 2051571475}, {"key": "okhttp3/internal/platform/AndroidPlatform$Companion.class", "name": "okhttp3/internal/platform/AndroidPlatform$Companion.class", "size": 1343, "crc": 1172633253}, {"key": "okhttp3/internal/platform/Jdk8WithJettyBootPlatform$Companion.class", "name": "okhttp3/internal/platform/Jdk8WithJettyBootPlatform$Companion.class", "size": 2995, "crc": -540879523}, {"key": "okhttp3/internal/platform/BouncyCastlePlatform$Companion.class", "name": "okhttp3/internal/platform/BouncyCastlePlatform$Companion.class", "size": 1350, "crc": -1135294793}, {"key": "okhttp3/internal/platform/AndroidPlatform$CustomTrustRootIndex.class", "name": "okhttp3/internal/platform/AndroidPlatform$CustomTrustRootIndex.class", "size": 4328, "crc": 1329061699}, {"key": "okhttp3/internal/HostnamesKt.class", "name": "okhttp3/internal/HostnamesKt.class", "size": 5736, "crc": -25887950}, {"key": "okhttp3/internal/http2/Http2Reader$ContinuationSource.class", "name": "okhttp3/internal/http2/Http2Reader$ContinuationSource.class", "size": 4355, "crc": 1636290259}, {"key": "okhttp3/internal/http2/Http2Connection$Listener.class", "name": "okhttp3/internal/http2/Http2Connection$Listener.class", "size": 1927, "crc": 852051150}, {"key": "okhttp3/internal/http2/Http2Connection$writeSynResetLater$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$writeSynResetLater$$inlined$execute$default$1.class", "size": 2393, "crc": 1427652149}, {"key": "okhttp3/internal/http2/Http2Connection$Builder.class", "name": "okhttp3/internal/http2/Http2Connection$Builder.class", "size": 8226, "crc": -1572615147}, {"key": "okhttp3/internal/http2/Hu<PERSON>man$Node.class", "name": "okhttp3/internal/http2/Hu<PERSON>man$Node.class", "size": 1500, "crc": 16402990}, {"key": "okhttp3/internal/http2/ErrorCode.class", "name": "okhttp3/internal/http2/ErrorCode.class", "size": 2743, "crc": -1714997956}, {"key": "okhttp3/internal/http2/Hpack$Reader.class", "name": "okhttp3/internal/http2/Hpack$Reader.class", "size": 8634, "crc": 1498712516}, {"key": "okhttp3/internal/http2/Http2Connection$Listener$Companion$REFUSE_INCOMING_STREAMS$1.class", "name": "okhttp3/internal/http2/Http2Connection$Listener$Companion$REFUSE_INCOMING_STREAMS$1.class", "size": 1508, "crc": -1577720540}, {"key": "okhttp3/internal/http2/ConnectionShutdownException.class", "name": "okhttp3/internal/http2/ConnectionShutdownException.class", "size": 576, "crc": 1850923606}, {"key": "okhttp3/internal/http2/Http2.class", "name": "okhttp3/internal/http2/Http2.class", "size": 5469, "crc": 942832336}, {"key": "okhttp3/internal/http2/PushObserver.class", "name": "okhttp3/internal/http2/PushObserver.class", "size": 1828, "crc": 323252283}, {"key": "okhttp3/internal/http2/Http2Connection$ReaderRunnable$headers$lambda$2$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$ReaderRunnable$headers$lambda$2$$inlined$execute$default$1.class", "size": 3905, "crc": 1743768556}, {"key": "okhttp3/internal/http2/Http2ExchangeCodec$Companion.class", "name": "okhttp3/internal/http2/Http2ExchangeCodec$Companion.class", "size": 5432, "crc": 305997677}, {"key": "okhttp3/internal/http2/Http2Connection$special$$inlined$schedule$1.class", "name": "okhttp3/internal/http2/Http2Connection$special$$inlined$schedule$1.class", "size": 2664, "crc": 1931691042}, {"key": "okhttp3/internal/http2/Http2Connection.class", "name": "okhttp3/internal/http2/Http2Connection.class", "size": 31321, "crc": 2085830143}, {"key": "okhttp3/internal/http2/Http2Stream$StreamTimeout.class", "name": "okhttp3/internal/http2/Http2Stream$StreamTimeout.class", "size": 2227, "crc": 1136147972}, {"key": "okhttp3/internal/http2/PushObserver$Companion$PushObserverCancel.class", "name": "okhttp3/internal/http2/PushObserver$Companion$PushObserverCancel.class", "size": 2266, "crc": -1658175565}, {"key": "okhttp3/internal/http2/Hpack$Writer.class", "name": "okhttp3/internal/http2/Hpack$Writer.class", "size": 7244, "crc": 616572129}, {"key": "okhttp3/internal/http2/Http2Connection$ReaderRunnable$settings$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$ReaderRunnable$settings$$inlined$execute$default$1.class", "size": 2230, "crc": 1327838368}, {"key": "okhttp3/internal/http2/Http2ExchangeCodec.class", "name": "okhttp3/internal/http2/Http2ExchangeCodec.class", "size": 7701, "crc": 1735150889}, {"key": "okhttp3/internal/http2/Http2Connection$Companion.class", "name": "okhttp3/internal/http2/Http2Connection$Companion.class", "size": 1375, "crc": 1017572103}, {"key": "okhttp3/internal/http2/PushObserver$Companion.class", "name": "okhttp3/internal/http2/PushObserver$Companion.class", "size": 895, "crc": 1099510193}, {"key": "okhttp3/internal/http2/Header$Companion.class", "name": "okhttp3/internal/http2/Header$Companion.class", "size": 1284, "crc": -649439794}, {"key": "okhttp3/internal/http2/ErrorCode$Companion.class", "name": "okhttp3/internal/http2/ErrorCode$Companion.class", "size": 1833, "crc": -1307592430}, {"key": "okhttp3/internal/http2/Http2Connection$ReaderRunnable.class", "name": "okhttp3/internal/http2/Http2Connection$ReaderRunnable.class", "size": 17251, "crc": 9893797}, {"key": "okhttp3/internal/http2/Http2Connection$Listener$Companion.class", "name": "okhttp3/internal/http2/Http2Connection$Listener$Companion.class", "size": 1019, "crc": 2118742497}, {"key": "okhttp3/internal/http2/Http2Writer$Companion.class", "name": "okhttp3/internal/http2/Http2Writer$Companion.class", "size": 909, "crc": -785646609}, {"key": "okhttp3/internal/http2/Settings$Companion.class", "name": "okhttp3/internal/http2/Settings$Companion.class", "size": 1135, "crc": 1775901465}, {"key": "okhttp3/internal/http2/Http2Connection$ReaderRunnable$ping$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$ReaderRunnable$ping$$inlined$execute$default$1.class", "size": 2115, "crc": -2027680711}, {"key": "okhttp3/internal/http2/Http2Writer.class", "name": "okhttp3/internal/http2/Http2Writer.class", "size": 10242, "crc": 144002809}, {"key": "okhttp3/internal/http2/Http2Connection$sendDegradedPingLater$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$sendDegradedPingLater$$inlined$execute$default$1.class", "size": 1966, "crc": -1768304744}, {"key": "okhttp3/internal/http2/Http2Stream$Companion.class", "name": "okhttp3/internal/http2/Http2Stream$Companion.class", "size": 852, "crc": -1453460707}, {"key": "okhttp3/internal/http2/StreamResetException.class", "name": "okhttp3/internal/http2/StreamResetException.class", "size": 1275, "crc": -232545786}, {"key": "okhttp3/internal/http2/Http2Connection$pushResetLater$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$pushResetLater$$inlined$execute$default$1.class", "size": 2796, "crc": 1688364869}, {"key": "okhttp3/internal/http2/Http2Stream$FramingSource.class", "name": "okhttp3/internal/http2/Http2Stream$FramingSource.class", "size": 9377, "crc": -507341681}, {"key": "okhttp3/internal/http2/Http2Reader$Handler.class", "name": "okhttp3/internal/http2/Http2Reader$Handler.class", "size": 2459, "crc": 1217026013}, {"key": "okhttp3/internal/http2/Http2Connection$pushHeadersLater$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$pushHeadersLater$$inlined$execute$default$1.class", "size": 3692, "crc": 1565099590}, {"key": "okhttp3/internal/http2/Http2Connection$ReaderRunnable$applyAndAckSettings$lambda$7$lambda$6$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$ReaderRunnable$applyAndAckSettings$lambda$7$lambda$6$$inlined$execute$default$1.class", "size": 2623, "crc": 28075783}, {"key": "okhttp3/internal/http2/Http2Connection$pushRequestLater$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$pushRequestLater$$inlined$execute$default$1.class", "size": 3613, "crc": 45941606}, {"key": "okhttp3/internal/http2/Header.class", "name": "okhttp3/internal/http2/Header.class", "size": 4181, "crc": 209851347}, {"key": "okhttp3/internal/http2/Http2Connection$pushDataLater$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$pushDataLater$$inlined$execute$default$1.class", "size": 3744, "crc": 2115540884}, {"key": "okhttp3/internal/http2/Hu<PERSON>man.class", "name": "okhttp3/internal/http2/Hu<PERSON>man.class", "size": 12241, "crc": -638219141}, {"key": "okhttp3/internal/http2/Http2Stream$FramingSink.class", "name": "okhttp3/internal/http2/Http2Stream$FramingSink.class", "size": 7584, "crc": 1427886343}, {"key": "okhttp3/internal/http2/Settings.class", "name": "okhttp3/internal/http2/Settings.class", "size": 3648, "crc": -1235821747}, {"key": "okhttp3/internal/http2/Http2Reader$Companion.class", "name": "okhttp3/internal/http2/Http2Reader$Companion.class", "size": 1755, "crc": -783233826}, {"key": "okhttp3/internal/http2/Http2Stream.class", "name": "okhttp3/internal/http2/Http2Stream.class", "size": 16703, "crc": -1852748510}, {"key": "okhttp3/internal/http2/Hpack.class", "name": "okhttp3/internal/http2/Hpack.class", "size": 6198, "crc": -1870451821}, {"key": "okhttp3/internal/http2/Http2Reader.class", "name": "okhttp3/internal/http2/Http2Reader.class", "size": 12327, "crc": 86428932}, {"key": "okhttp3/internal/http2/Http2Connection$writeWindowUpdateLater$$inlined$execute$default$1.class", "name": "okhttp3/internal/http2/Http2Connection$writeWindowUpdateLater$$inlined$execute$default$1.class", "size": 2422, "crc": -301516373}, {"key": "okhttp3/internal/http/RetryAndFollowUpInterceptor.class", "name": "okhttp3/internal/http/RetryAndFollowUpInterceptor.class", "size": 11131, "crc": 1931863993}, {"key": "okhttp3/internal/http/HttpMethod.class", "name": "okhttp3/internal/http/HttpMethod.class", "size": 1969, "crc": 65813748}, {"key": "okhttp3/internal/http/HttpHeaders.class", "name": "okhttp3/internal/http/HttpHeaders.class", "size": 7899, "crc": 771701243}, {"key": "okhttp3/internal/http/RealInterceptorChain.class", "name": "okhttp3/internal/http/RealInterceptorChain.class", "size": 8519, "crc": -167801484}, {"key": "okhttp3/internal/http/RetryAndFollowUpInterceptor$Companion.class", "name": "okhttp3/internal/http/RetryAndFollowUpInterceptor$Companion.class", "size": 911, "crc": -15821116}, {"key": "okhttp3/internal/http/RequestLine.class", "name": "okhttp3/internal/http/RequestLine.class", "size": 2647, "crc": -921909874}, {"key": "okhttp3/internal/http/ExchangeCodec.class", "name": "okhttp3/internal/http/ExchangeCodec.class", "size": 2171, "crc": 1240382676}, {"key": "okhttp3/internal/http/BridgeInterceptor.class", "name": "okhttp3/internal/http/BridgeInterceptor.class", "size": 7269, "crc": -886184302}, {"key": "okhttp3/internal/http/DatesKt$STANDARD_DATE_FORMAT$1.class", "name": "okhttp3/internal/http/DatesKt$STANDARD_DATE_FORMAT$1.class", "size": 1600, "crc": 1533846658}, {"key": "okhttp3/internal/http/ExchangeCodec$Companion.class", "name": "okhttp3/internal/http/ExchangeCodec$Companion.class", "size": 808, "crc": -2118605464}, {"key": "okhttp3/internal/http/DatesKt.class", "name": "okhttp3/internal/http/DatesKt.class", "size": 4101, "crc": 1614952123}, {"key": "okhttp3/internal/http/StatusLine.class", "name": "okhttp3/internal/http/StatusLine.class", "size": 2396, "crc": 1014600211}, {"key": "okhttp3/internal/http/CallServerInterceptor.class", "name": "okhttp3/internal/http/CallServerInterceptor.class", "size": 6295, "crc": 1837490226}, {"key": "okhttp3/internal/http/RealResponseBody.class", "name": "okhttp3/internal/http/RealResponseBody.class", "size": 1854, "crc": -2018680385}, {"key": "okhttp3/internal/http/StatusLine$Companion.class", "name": "okhttp3/internal/http/StatusLine$Companion.class", "size": 3524, "crc": 1989197351}, {"key": "okhttp3/internal/concurrent/TaskRunner$runnable$1.class", "name": "okhttp3/internal/concurrent/TaskRunner$runnable$1.class", "size": 4480, "crc": -2123840904}, {"key": "okhttp3/internal/concurrent/TaskQueue$execute$1.class", "name": "okhttp3/internal/concurrent/TaskQueue$execute$1.class", "size": 1694, "crc": -1733766641}, {"key": "okhttp3/internal/concurrent/TaskRunner$RealBackend.class", "name": "okhttp3/internal/concurrent/TaskRunner$RealBackend.class", "size": 3653, "crc": 1434415280}, {"key": "okhttp3/internal/concurrent/TaskLoggerKt.class", "name": "okhttp3/internal/concurrent/TaskLoggerKt.class", "size": 5191, "crc": 1103053555}, {"key": "okhttp3/internal/concurrent/TaskQueue$schedule$2.class", "name": "okhttp3/internal/concurrent/TaskQueue$schedule$2.class", "size": 1756, "crc": 2049810001}, {"key": "okhttp3/internal/concurrent/TaskQueue$AwaitIdleTask.class", "name": "okhttp3/internal/concurrent/TaskQueue$AwaitIdleTask.class", "size": 1497, "crc": 935124998}, {"key": "okhttp3/internal/concurrent/TaskRunner$Backend.class", "name": "okhttp3/internal/concurrent/TaskRunner$Backend.class", "size": 1087, "crc": -292579267}, {"key": "okhttp3/internal/concurrent/Task.class", "name": "okhttp3/internal/concurrent/Task.class", "size": 3074, "crc": 509036507}, {"key": "okhttp3/internal/concurrent/TaskRunner$Companion.class", "name": "okhttp3/internal/concurrent/TaskRunner$Companion.class", "size": 1194, "crc": -1109465050}, {"key": "okhttp3/internal/concurrent/TaskRunner.class", "name": "okhttp3/internal/concurrent/TaskRunner.class", "size": 10963, "crc": 1007916744}, {"key": "okhttp3/internal/concurrent/TaskQueue.class", "name": "okhttp3/internal/concurrent/TaskQueue.class", "size": 13079, "crc": -1519406996}, {"key": "okhttp3/internal/io/FileSystem.class", "name": "okhttp3/internal/io/FileSystem.class", "size": 1836, "crc": 1340078747}, {"key": "okhttp3/internal/io/FileSystem$Companion$SystemFileSystem.class", "name": "okhttp3/internal/io/FileSystem$Companion$SystemFileSystem.class", "size": 3848, "crc": -1132128461}, {"key": "okhttp3/internal/io/FileSystem$Companion.class", "name": "okhttp3/internal/io/FileSystem$Companion.class", "size": 864, "crc": 661121761}, {"key": "okhttp3/internal/cache2/Relay$Companion.class", "name": "okhttp3/internal/cache2/Relay$Companion.class", "size": 3453, "crc": 52152326}, {"key": "okhttp3/internal/cache2/Relay$RelaySource.class", "name": "okhttp3/internal/cache2/Relay$RelaySource.class", "size": 6401, "crc": 1566952087}, {"key": "okhttp3/internal/cache2/Relay.class", "name": "okhttp3/internal/cache2/Relay.class", "size": 7520, "crc": 1043211213}, {"key": "okhttp3/internal/cache2/FileOperator.class", "name": "okhttp3/internal/cache2/FileOperator.class", "size": 2124, "crc": 1214657113}, {"key": "okhttp3/internal/Util.class", "name": "okhttp3/internal/Util.class", "size": 34690, "crc": -1625463258}, {"key": "okhttp3/CacheControl$Builder.class", "name": "okhttp3/CacheControl$Builder.class", "size": 5248, "crc": -1295531864}, {"key": "okhttp3/Authenticator$Companion$AuthenticatorNone.class", "name": "okhttp3/Authenticator$Companion$AuthenticatorNone.class", "size": 1291, "crc": -1105021123}, {"key": "okhttp3/Handshake$Companion.class", "name": "okhttp3/Handshake$Companion.class", "size": 5851, "crc": 1744009965}, {"key": "okhttp3/Headers$Companion.class", "name": "okhttp3/Headers$Companion.class", "size": 6919, "crc": -1928069628}, {"key": "okhttp3/ResponseBody.class", "name": "okhttp3/ResponseBody.class", "size": 9126, "crc": 1777596273}, {"key": "okhttp3/Cache$CacheResponseBody.class", "name": "okhttp3/Cache$CacheResponseBody.class", "size": 2801, "crc": 955405621}, {"key": "okhttp3/Handshake$Companion$handshake$1.class", "name": "okhttp3/Handshake$Companion$handshake$1.class", "size": 1483, "crc": -511531612}, {"key": "okhttp3/ConnectionSpec.class", "name": "okhttp3/ConnectionSpec.class", "size": 11241, "crc": 357434953}, {"key": "okhttp3/EventListener.class", "name": "okhttp3/EventListener.class", "size": 7219, "crc": 1708714614}, {"key": "okhttp3/Cookie$Builder.class", "name": "okhttp3/Cookie$Builder.class", "size": 5720, "crc": -1744663806}, {"key": "okhttp3/Headers.class", "name": "okhttp3/Headers.class", "size": 8697, "crc": -583312219}, {"key": "okhttp3/Connection.class", "name": "okhttp3/Connection.class", "size": 814, "crc": 1344918540}, {"key": "okhttp3/HttpUrl$Companion.class", "name": "okhttp3/HttpUrl$Companion.class", "size": 11755, "crc": -518054976}, {"key": "okhttp3/MediaType$Companion.class", "name": "okhttp3/MediaType$Companion.class", "size": 6308, "crc": -1986662319}, {"key": "okhttp3/OkHttpClient$Builder.class", "name": "okhttp3/OkHttpClient$Builder.class", "size": 31635, "crc": 1677833197}, {"key": "okhttp3/MultipartBody$Part.class", "name": "okhttp3/MultipartBody$Part.class", "size": 3097, "crc": 185054644}, {"key": "okhttp3/Authenticator$Companion.class", "name": "okhttp3/Authenticator$Companion.class", "size": 875, "crc": 500091654}, {"key": "okhttp3/EventListener$Factory.class", "name": "okhttp3/EventListener$Factory.class", "size": 705, "crc": -2113115550}, {"key": "okhttp3/Cache$urls$1.class", "name": "okhttp3/Cache$urls$1.class", "size": 3549, "crc": 988190685}, {"key": "okhttp3/Cache$Entry.class", "name": "okhttp3/Cache$Entry.class", "size": 14241, "crc": 160886082}, {"key": "okhttp3/RequestBody.class", "name": "okhttp3/RequestBody.class", "size": 4800, "crc": -2123427107}, {"key": "okhttp3/OkHttp.class", "name": "okhttp3/OkHttp.class", "size": 747, "crc": 1427437689}, {"key": "okhttp3/ConnectionSpec$Builder.class", "name": "okhttp3/ConnectionSpec$Builder.class", "size": 9339, "crc": 1542269457}, {"key": "okhttp3/FormBody$Companion.class", "name": "okhttp3/FormBody$Companion.class", "size": 813, "crc": 1354194368}, {"key": "okhttp3/RequestBody$Companion$toRequestBody$1.class", "name": "okhttp3/RequestBody$Companion$toRequestBody$1.class", "size": 1777, "crc": -2064222657}, {"key": "okhttp3/MultipartReader$Companion.class", "name": "okhttp3/MultipartReader$Companion.class", "size": 1084, "crc": -1852972015}, {"key": "okhttp3/Cookie.class", "name": "okhttp3/Cookie.class", "size": 8698, "crc": 454585268}, {"key": "okhttp3/MultipartBody.class", "name": "okhttp3/MultipartBody.class", "size": 6871, "crc": 1881639725}, {"key": "okhttp3/TlsVersion.class", "name": "okhttp3/TlsVersion.class", "size": 2630, "crc": -1818013582}, {"key": "okhttp3/Interceptor$Companion.class", "name": "okhttp3/Interceptor$Companion.class", "size": 1657, "crc": -1255702403}, {"key": "okhttp3/ResponseBody$Companion.class", "name": "okhttp3/ResponseBody$Companion.class", "size": 6014, "crc": -2061189506}, {"key": "okhttp3/MediaType.class", "name": "okhttp3/MediaType.class", "size": 5437, "crc": -86069594}, {"key": "okhttp3/Handshake$peerCertificates$2.class", "name": "okhttp3/Handshake$peerCertificates$2.class", "size": 1837, "crc": 1057690575}, {"key": "okhttp3/Dns.class", "name": "okhttp3/Dns.class", "size": 1125, "crc": 841254532}, {"key": "okhttp3/OkHttpClient.class", "name": "okhttp3/OkHttpClient.class", "size": 21204, "crc": -29363691}, {"key": "okhttp3/RequestBody$Companion$toRequestBody$2.class", "name": "okhttp3/RequestBody$Companion$toRequestBody$2.class", "size": 1778, "crc": 1132089286}, {"key": "okhttp3/HttpUrl$Builder.class", "name": "okhttp3/HttpUrl$Builder.class", "size": 26358, "crc": 24290013}, {"key": "okhttp3/HttpUrl.class", "name": "okhttp3/HttpUrl.class", "size": 17937, "crc": 1653760556}, {"key": "okhttp3/Cache$CacheResponseBody$1.class", "name": "okhttp3/Cache$CacheResponseBody$1.class", "size": 1312, "crc": -244139700}, {"key": "okhttp3/ConnectionSpec$Companion.class", "name": "okhttp3/ConnectionSpec$Companion.class", "size": 1141, "crc": -1438613723}, {"key": "okhttp3/CookieJar$Companion.class", "name": "okhttp3/CookieJar$Companion.class", "size": 788, "crc": 1485121443}, {"key": "okhttp3/WebSocket.class", "name": "okhttp3/WebSocket.class", "size": 1061, "crc": 1322887735}, {"key": "okhttp3/Address.class", "name": "okhttp3/Address.class", "size": 9122, "crc": 1858776922}, {"key": "okhttp3/Dns$Companion.class", "name": "okhttp3/Dns$Companion.class", "size": 748, "crc": 255568453}, {"key": "okhttp3/CertificatePinner$check$1.class", "name": "okhttp3/CertificatePinner$check$1.class", "size": 3809, "crc": -1666871162}, {"key": "okhttp3/Handshake$Companion$get$1.class", "name": "okhttp3/Handshake$Companion$get$1.class", "size": 1518, "crc": -1536072779}, {"key": "okhttp3/Credentials.class", "name": "okhttp3/Credentials.class", "size": 2328, "crc": 1090277036}, {"key": "okhttp3/FormBody$Builder.class", "name": "okhttp3/FormBody$Builder.class", "size": 3126, "crc": 791506025}, {"key": "okhttp3/ConnectionPool.class", "name": "okhttp3/ConnectionPool.class", "size": 2203, "crc": -876349255}, {"key": "okhttp3/Protocol$Companion.class", "name": "okhttp3/Protocol$Companion.class", "size": 2000, "crc": 1116952937}, {"key": "okhttp3/CookieJar.class", "name": "okhttp3/CookieJar.class", "size": 1324, "crc": 735388176}, {"key": "okhttp3/WebSocketListener.class", "name": "okhttp3/WebSocketListener.class", "size": 2182, "crc": -1329573479}, {"key": "okhttp3/Interceptor$Chain.class", "name": "okhttp3/Interceptor$Chain.class", "size": 1547, "crc": 526669368}, {"key": "okhttp3/Cache$Companion.class", "name": "okhttp3/Cache$Companion.class", "size": 7116, "crc": 1228265548}, {"key": "okhttp3/Cookie$Companion.class", "name": "okhttp3/Cookie$Companion.class", "size": 11786, "crc": -2001160834}, {"key": "okhttp3/Dns$Companion$DnsSystem.class", "name": "okhttp3/Dns$Companion$DnsSystem.class", "size": 2167, "crc": -69325627}, {"key": "okhttp3/Protocol.class", "name": "okhttp3/Protocol.class", "size": 2654, "crc": 57594102}, {"key": "okhttp3/CipherSuite$Companion.class", "name": "okhttp3/CipherSuite$Companion.class", "size": 10328, "crc": 402225788}, {"key": "okhttp3/Call.class", "name": "okhttp3/Call.class", "size": 1163, "crc": 1902289567}, {"key": "okhttp3/CertificatePinner$Builder.class", "name": "okhttp3/CertificatePinner$Builder.class", "size": 2424, "crc": -1159041864}, {"key": "okhttp3/Request.class", "name": "okhttp3/Request.class", "size": 7634, "crc": 1642367218}, {"key": "okhttp3/MultipartBody$Part$Companion.class", "name": "okhttp3/MultipartBody$Part$Companion.class", "size": 4714, "crc": -705332113}, {"key": "okhttp3/CookieJar$Companion$NoCookies.class", "name": "okhttp3/CookieJar$Companion$NoCookies.class", "size": 1602, "crc": -490915537}, {"key": "okhttp3/CipherSuite$Companion$ORDER_BY_NAME$1.class", "name": "okhttp3/CipherSuite$Companion$ORDER_BY_NAME$1.class", "size": 1825, "crc": 477761744}, {"key": "okhttp3/Dispatcher.class", "name": "okhttp3/Dispatcher.class", "size": 12044, "crc": 1820351287}, {"key": "okhttp3/Handshake.class", "name": "okhttp3/Handshake.class", "size": 9069, "crc": -1190874058}, {"key": "okhttp3/OkHttpClient$Builder$addInterceptor$2.class", "name": "okhttp3/OkHttpClient$Builder$addInterceptor$2.class", "size": 2071, "crc": 1938429168}, {"key": "okhttp3/Call$Factory.class", "name": "okhttp3/Call$Factory.class", "size": 661, "crc": -895086121}, {"key": "okhttp3/Request$Builder.class", "name": "okhttp3/Request$Builder.class", "size": 11265, "crc": -589197392}, {"key": "okhttp3/ResponseBody$Companion$asResponseBody$1.class", "name": "okhttp3/ResponseBody$Companion$asResponseBody$1.class", "size": 1494, "crc": -1296879837}, {"key": "okhttp3/OkHttpClient$Builder$addNetworkInterceptor$2.class", "name": "okhttp3/OkHttpClient$Builder$addNetworkInterceptor$2.class", "size": 2106, "crc": -879763607}, {"key": "okhttp3/CacheControl.class", "name": "okhttp3/CacheControl.class", "size": 7436, "crc": 1187184060}, {"key": "okhttp3/Cache.class", "name": "okhttp3/Cache.class", "size": 10122, "crc": -1015896960}, {"key": "okhttp3/ResponseBody$BomAwareReader.class", "name": "okhttp3/ResponseBody$BomAwareReader.class", "size": 3048, "crc": -190423099}, {"key": "okhttp3/OkHttpClient$Companion.class", "name": "okhttp3/OkHttpClient$Companion.class", "size": 1470, "crc": -144582430}, {"key": "okhttp3/Challenge.class", "name": "okhttp3/Challenge.class", "size": 5736, "crc": -1403244857}, {"key": "okhttp3/CacheControl$Companion.class", "name": "okhttp3/CacheControl$Companion.class", "size": 4665, "crc": -771496996}, {"key": "okhttp3/Callback.class", "name": "okhttp3/Callback.class", "size": 792, "crc": 1361643579}, {"key": "okhttp3/Route.class", "name": "okhttp3/Route.class", "size": 3601, "crc": -1786469694}, {"key": "okhttp3/Response.class", "name": "okhttp3/Response.class", "size": 11568, "crc": 133881036}, {"key": "okhttp3/MultipartBody$Companion.class", "name": "okhttp3/MultipartBody$Companion.class", "size": 2120, "crc": 543611505}, {"key": "okhttp3/RequestBody$Companion$asRequestBody$1.class", "name": "okhttp3/RequestBody$Companion$asRequestBody$1.class", "size": 2692, "crc": -1790187242}, {"key": "okhttp3/RequestBody$Companion.class", "name": "okhttp3/RequestBody$Companion.class", "size": 7497, "crc": 49273443}, {"key": "okhttp3/Cache$Entry$Companion.class", "name": "okhttp3/Cache$Entry$Companion.class", "size": 872, "crc": 1788974577}, {"key": "okhttp3/Interceptor$Companion$invoke$1.class", "name": "okhttp3/Interceptor$Companion$invoke$1.class", "size": 1983, "crc": -944493307}, {"key": "okhttp3/CertificatePinner$Pin.class", "name": "okhttp3/CertificatePinner$Pin.class", "size": 5143, "crc": 1300181482}, {"key": "okhttp3/CertificatePinner$Companion.class", "name": "okhttp3/CertificatePinner$Companion.class", "size": 3273, "crc": -1324839013}, {"key": "okhttp3/Cache$RealCacheRequest.class", "name": "okhttp3/Cache$RealCacheRequest.class", "size": 2960, "crc": 509539323}, {"key": "okhttp3/FormBody.class", "name": "okhttp3/FormBody.class", "size": 4499, "crc": 1754618063}, {"key": "okhttp3/MultipartReader$PartSource.class", "name": "okhttp3/MultipartReader$PartSource.class", "size": 4840, "crc": 1574607207}, {"key": "okhttp3/EventListener$Companion$NONE$1.class", "name": "okhttp3/EventListener$Companion$NONE$1.class", "size": 594, "crc": 180187888}, {"key": "okhttp3/TlsVersion$Companion.class", "name": "okhttp3/TlsVersion$Companion.class", "size": 2014, "crc": 1352191645}, {"key": "okhttp3/Response$Builder.class", "name": "okhttp3/Response$Builder.class", "size": 13924, "crc": -1091286967}, {"key": "okhttp3/Authenticator.class", "name": "okhttp3/Authenticator.class", "size": 1443, "crc": 1597561962}, {"key": "okhttp3/MultipartBody$Builder.class", "name": "okhttp3/MultipartBody$Builder.class", "size": 6412, "crc": 1422474245}, {"key": "okhttp3/WebSocket$Factory.class", "name": "okhttp3/WebSocket$Factory.class", "size": 791, "crc": 1395296728}, {"key": "okhttp3/MultipartReader$Part.class", "name": "okhttp3/MultipartReader$Part.class", "size": 1531, "crc": 422731616}, {"key": "META-INF/okhttp.kotlin_module", "name": "META-INF/okhttp.kotlin_module", "size": 277, "crc": -247790812}, {"key": "okhttp3/internal/publicsuffix/publicsuffixes.gz", "name": "okhttp3/internal/publicsuffix/publicsuffixes.gz", "size": 41394, "crc": -174086922}, {"key": "okhttp3/internal/publicsuffix/NOTICE", "name": "okhttp3/internal/publicsuffix/NOTICE", "size": 218, "crc": -550320132}, {"key": "META-INF/proguard/okhttp3.pro", "name": "META-INF/proguard/okhttp3.pro", "size": 622, "crc": -1662877269}]