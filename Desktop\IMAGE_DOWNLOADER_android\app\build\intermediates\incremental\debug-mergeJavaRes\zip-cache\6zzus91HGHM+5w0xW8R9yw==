[{"key": "androidx/compose/ui/tooling/ComposableInvoker.class", "name": "androidx/compose/ui/tooling/ComposableInvoker.class", "size": 16688, "crc": 1240376883}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-1$1.class", "size": 2322, "crc": 2119931448}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-2$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-2$1.class", "size": 2323, "crc": 1907576538}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-3$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda-3$1.class", "size": 2323, "crc": -2143749060}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt.class", "size": 2346, "crc": -1205243852}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt$lambda-1$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt$lambda-1$1.class", "size": 2798, "crc": -1570229516}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt.class", "size": 1573, "crc": 1519609673}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1$activityResultRegistry$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1$activityResultRegistry$1.class", "size": 1901, "crc": -1024283126}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1.class", "size": 1720, "crc": 601499395}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeOnBackPressedDispatcherOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeOnBackPressedDispatcherOwner$1.class", "size": 2253, "crc": -1844083729}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeSavedStateRegistryOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeSavedStateRegistryOwner$1.class", "size": 3017, "crc": 1126036777}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeViewModelStoreOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeViewModelStoreOwner$1.class", "size": 1328, "crc": 798076190}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$1.class", "size": 3144, "crc": 138306337}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$2.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$2.class", "size": 2314, "crc": -**********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$1.class", "size": 1547, "crc": 933531869}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$2.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$2.class", "size": 1261, "crc": -406275828}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$findDesignInfoProviders$1$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$findDesignInfoProviders$1$1.class", "size": 3636, "crc": -**********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$1.class", "size": 1357, "crc": -**********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$2.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$2.class", "size": 1357, "crc": **********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1$1$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1$1$1.class", "size": 2215, "crc": **********}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1$composable$1$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1$composable$1$1.class", "size": 3239, "crc": 89686293}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1.class", "size": 6409, "crc": 322367843}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3.class", "size": 4125, "crc": 331036850}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$onDraw$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$onDraw$1.class", "size": 1171, "crc": -887622761}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter.class", "size": 36140, "crc": 212972855}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter_androidKt.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter_androidKt.class", "size": 1473, "crc": -1614208368}, {"key": "androidx/compose/ui/tooling/CompositionDataRecord$Companion.class", "name": "androidx/compose/ui/tooling/CompositionDataRecord$Companion.class", "size": 1114, "crc": 199738675}, {"key": "androidx/compose/ui/tooling/CompositionDataRecord.class", "name": "androidx/compose/ui/tooling/CompositionDataRecord.class", "size": 1042, "crc": -410588051}, {"key": "androidx/compose/ui/tooling/CompositionDataRecordImpl.class", "name": "androidx/compose/ui/tooling/CompositionDataRecordImpl.class", "size": 1348, "crc": 1879826069}, {"key": "androidx/compose/ui/tooling/InspectableKt$InInspectionModeOnly$1.class", "name": "androidx/compose/ui/tooling/InspectableKt$InInspectionModeOnly$1.class", "size": 1974, "crc": 1826958828}, {"key": "androidx/compose/ui/tooling/InspectableKt$Inspectable$1.class", "name": "androidx/compose/ui/tooling/InspectableKt$Inspectable$1.class", "size": 2216, "crc": -1079228227}, {"key": "androidx/compose/ui/tooling/InspectableKt.class", "name": "androidx/compose/ui/tooling/InspectableKt.class", "size": 6848, "crc": 648603796}, {"key": "androidx/compose/ui/tooling/LayoutlibFontResourceLoader.class", "name": "androidx/compose/ui/tooling/LayoutlibFontResourceLoader.class", "size": 2737, "crc": 897175747}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setComposableContent$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setComposableContent$2.class", "size": 2642, "crc": 541420370}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1$1$1.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1$1$1.class", "size": 1592, "crc": 729517066}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1.class", "size": 5315, "crc": 611616413}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$2.class", "size": 10295, "crc": -1759686079}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1.class", "size": 5683, "crc": 1178662226}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$2.class", "size": 2861, "crc": -2143857866}, {"key": "androidx/compose/ui/tooling/PreviewActivity.class", "name": "androidx/compose/ui/tooling/PreviewActivity.class", "size": 5210, "crc": 1082987639}, {"key": "androidx/compose/ui/tooling/PreviewLogger$Companion.class", "name": "androidx/compose/ui/tooling/PreviewLogger$Companion.class", "size": 1992, "crc": -1063118945}, {"key": "androidx/compose/ui/tooling/PreviewLogger.class", "name": "androidx/compose/ui/tooling/PreviewLogger.class", "size": 1025, "crc": -1946377969}, {"key": "androidx/compose/ui/tooling/PreviewLogger_androidKt.class", "name": "androidx/compose/ui/tooling/PreviewLogger_androidKt.class", "size": 528, "crc": 1896162898}, {"key": "androidx/compose/ui/tooling/PreviewUtils_androidKt.class", "name": "androidx/compose/ui/tooling/PreviewUtils_androidKt.class", "size": 11777, "crc": -953800774}, {"key": "androidx/compose/ui/tooling/ResourceFontHelper.class", "name": "androidx/compose/ui/tooling/ResourceFontHelper.class", "size": 1545, "crc": 1222514080}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo$allNodes$1.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo$allNodes$1.class", "size": 6152, "crc": -370837351}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo.class", "size": 6402, "crc": -1217606623}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$1.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$1.class", "size": 2711, "crc": 1580798436}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$2.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$2.class", "size": 2193, "crc": 2117922871}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$3.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt$stitchTrees$1$3.class", "size": 2014, "crc": 1121942880}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt.class", "size": 8086, "crc": 1986905970}, {"key": "androidx/compose/ui/tooling/ThreadSafeException.class", "name": "androidx/compose/ui/tooling/ThreadSafeException.class", "size": 1819, "crc": 2135755060}, {"key": "androidx/compose/ui/tooling/ViewInfo.class", "name": "androidx/compose/ui/tooling/ViewInfo.class", "size": 8402, "crc": -1335646668}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$filterTree$1.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$filterTree$1.class", "size": 1683, "crc": 43099749}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$1.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$1.class", "size": 1695, "crc": -1008584503}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$2.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$2.class", "size": 1800, "crc": 1775879044}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$3.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$3.class", "size": 1856, "crc": 1029870297}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$4.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt$toDebugString$4.class", "size": 1917, "crc": 239778}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt.class", "size": 7791, "crc": -867681856}, {"key": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation$Companion.class", "size": 3234, "crc": -342968807}, {"key": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation.class", "size": 7433, "crc": 672103653}, {"key": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation$Companion.class", "size": 3287, "crc": 590592045}, {"key": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation.class", "size": 5466, "crc": 1536527898}, {"key": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation.class", "size": 4120, "crc": -595607514}, {"key": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation_androidKt.class", "name": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation_androidKt.class", "size": 1510, "crc": -1976240937}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch$addAnimations$2$1$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch$addAnimations$2$1$1.class", "size": 2466, "crc": -729727571}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch$hasAnimation$1$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch$hasAnimation$1$1.class", "size": 2177, "crc": -1763422867}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch.class", "size": 5660, "crc": -1270340905}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearch.class", "size": 20370, "crc": 2076897450}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearchInfo.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearchInfo.class", "size": 5544, "crc": 724602343}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedContentSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedContentSearch.class", "size": 10237, "crc": 1805643357}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedVisibilitySearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedVisibilitySearch.class", "size": 10273, "crc": 1037262267}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$DecaySearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$DecaySearch.class", "size": 1853, "crc": -466394658}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearch.class", "size": 12594, "crc": 1121664250}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearchInfo.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearchInfo.class", "size": 4373, "crc": 990225576}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$RememberSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$RememberSearch.class", "size": 7192, "crc": -912475313}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$Search.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$Search.class", "size": 4394, "crc": 947933775}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$TargetBasedSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$TargetBasedSearch.class", "size": 1895, "crc": 338555151}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$TransitionSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$TransitionSearch.class", "size": 9662, "crc": -1633917194}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$animateXAsStateSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$animateXAsStateSearch$1.class", "size": 2279, "crc": 882810367}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$animatedContentSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$animatedContentSearch$1.class", "size": 2071, "crc": 570076288}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$animatedVisibilitySearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$animatedVisibilitySearch$1.class", "size": 2204, "crc": 822671553}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$attachAllAnimations$1$groups$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$attachAllAnimations$1$groups$1.class", "size": 1680, "crc": 1157867517}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$infiniteTransitionSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$infiniteTransitionSearch$1.class", "size": 2192, "crc": -886792181}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$searchAny$1$groups$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$searchAny$1$groups$1.class", "size": 1650, "crc": -511283774}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$transitionSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$transitionSearch$1.class", "size": 2056, "crc": 1088667038}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$1.class", "size": 1826, "crc": -308080281}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$2.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$2.class", "size": 2102, "crc": -551732869}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$3.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$unsupportedSearch$3.class", "size": 2066, "crc": -1144754290}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch.class", "size": 12404, "crc": -1241699082}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt$findRememberedData$rememberCalls$1$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt$findRememberedData$rememberCalls$1$1.class", "size": 2424, "crc": -1968945510}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt.class", "size": 14677, "crc": 2146954842}, {"key": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation$Companion.class", "size": 2672, "crc": 2038621649}, {"key": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation.class", "size": 5834, "crc": -986618941}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$1.class", "size": 1226, "crc": -2038846505}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimateXAsState$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimateXAsState$1.class", "size": 3783, "crc": -1219524639}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedContent$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedContent$1.class", "size": 3356, "crc": 1092196055}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedVisibility$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedVisibility$1.class", "size": 1385, "crc": -1675168665}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedVisibility$2.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackAnimatedVisibility$2.class", "size": 3610, "crc": 1784282211}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackInfiniteTransition$1$1$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackInfiniteTransition$1$1$1.class", "size": 3911, "crc": -950332604}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackInfiniteTransition$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackInfiniteTransition$1.class", "size": 3537, "crc": 105514261}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackTransition$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackTransition$1.class", "size": 3179, "crc": 695763389}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackUnsupported$1.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock$trackUnsupported$1.class", "size": 1696, "crc": -1951793520}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock.class", "size": 24017, "crc": 2032834922}, {"key": "androidx/compose/ui/tooling/animation/ToolingState.class", "name": "androidx/compose/ui/tooling/animation/ToolingState.class", "size": 2767, "crc": -1442022974}, {"key": "androidx/compose/ui/tooling/animation/TransitionBasedAnimation.class", "name": "androidx/compose/ui/tooling/animation/TransitionBasedAnimation.class", "size": 1042, "crc": -407099310}, {"key": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation.class", "size": 3128, "crc": -939821843}, {"key": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation_androidKt.class", "name": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation_androidKt.class", "size": 2230, "crc": 1638516137}, {"key": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation$Companion.class", "size": 1966, "crc": 427103996}, {"key": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation.class", "size": 4580, "crc": 691120288}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimateXAsStateClock.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimateXAsStateClock.class", "size": 8265, "crc": -364816010}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getAnimatedProperties$lambda$8$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getAnimatedProperties$lambda$8$$inlined$sortedBy$1.class", "size": 2541, "crc": -343745273}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getTransitions$lambda$4$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getTransitions$lambda$4$$inlined$sortedBy$1.class", "size": 2496, "crc": -313992817}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock.class", "size": 11908, "crc": 282242873}, {"key": "androidx/compose/ui/tooling/animation/clock/ComposeAnimationClock.class", "name": "androidx/compose/ui/tooling/animation/clock/ComposeAnimationClock.class", "size": 2747, "crc": -1627494663}, {"key": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock$1.class", "name": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock$1.class", "size": 1456, "crc": -532659322}, {"key": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock.class", "name": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock.class", "size": 13013, "crc": 1359017485}, {"key": "androidx/compose/ui/tooling/animation/clock/TransitionClock.class", "name": "androidx/compose/ui/tooling/animation/clock/TransitionClock.class", "size": 10120, "crc": 692131651}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$startTimeMs$2.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$startTimeMs$2.class", "size": 3326, "crc": 1365583815}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$values$2.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$values$2.class", "size": 2733, "crc": 1321831972}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$values$4.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt$createTransitionInfo$values$4.class", "size": 2923, "crc": -1333998780}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt.class", "size": 15644, "crc": -421747647}, {"key": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState$Companion.class", "name": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState$Companion.class", "size": 1484, "crc": -1301317207}, {"key": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState.class", "name": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState.class", "size": 3113, "crc": 375059955}, {"key": "androidx/compose/ui/tooling/animation/states/ComposeAnimationState.class", "name": "androidx/compose/ui/tooling/animation/states/ComposeAnimationState.class", "size": 469, "crc": 736594963}, {"key": "androidx/compose/ui/tooling/animation/states/TargetState.class", "name": "androidx/compose/ui/tooling/animation/states/TargetState.class", "size": 3357, "crc": -230609735}, {"key": "META-INF/androidx.compose.ui_ui-tooling.version", "name": "META-INF/androidx.compose.ui_ui-tooling.version", "size": 6, "crc": 501114004}, {"key": "META-INF/ui-tooling_release.kotlin_module", "name": "META-INF/ui-tooling_release.kotlin_module", "size": 417, "crc": -1896118866}]