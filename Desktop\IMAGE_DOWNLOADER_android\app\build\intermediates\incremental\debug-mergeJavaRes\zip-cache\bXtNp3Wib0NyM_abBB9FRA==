[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/lifecycle-common.kotlin_module", "name": "META-INF/lifecycle-common.kotlin_module", "size": 115, "crc": 1172196438}, {"key": "androidx/lifecycle/CompositeGeneratedAdaptersObserver.class", "name": "androidx/lifecycle/CompositeGeneratedAdaptersObserver.class", "size": 2196, "crc": -304525078}, {"key": "androidx/lifecycle/DefaultLifecycleObserver.class", "name": "androidx/lifecycle/DefaultLifecycleObserver.class", "size": 1561, "crc": 1138428020}, {"key": "androidx/lifecycle/DefaultLifecycleObserverAdapter$WhenMappings.class", "name": "androidx/lifecycle/DefaultLifecycleObserverAdapter$WhenMappings.class", "size": 1129, "crc": -1393967104}, {"key": "androidx/lifecycle/DefaultLifecycleObserverAdapter.class", "name": "androidx/lifecycle/DefaultLifecycleObserverAdapter.class", "size": 2529, "crc": 417874049}, {"key": "androidx/lifecycle/DispatchQueue.class", "name": "androidx/lifecycle/DispatchQueue.class", "size": 4230, "crc": 432147247}, {"key": "androidx/lifecycle/GeneratedAdapter.class", "name": "androidx/lifecycle/GeneratedAdapter.class", "size": 1304, "crc": -1578430450}, {"key": "androidx/lifecycle/Lifecycle$Event$Companion$WhenMappings.class", "name": "androidx/lifecycle/Lifecycle$Event$Companion$WhenMappings.class", "size": 1073, "crc": -1453898351}, {"key": "androidx/lifecycle/Lifecycle$Event$Companion.class", "name": "androidx/lifecycle/Lifecycle$Event$Companion.class", "size": 2619, "crc": 1637341488}, {"key": "androidx/lifecycle/Lifecycle$Event$WhenMappings.class", "name": "androidx/lifecycle/Lifecycle$Event$WhenMappings.class", "size": 1035, "crc": -1337925050}, {"key": "androidx/lifecycle/Lifecycle$Event.class", "name": "androidx/lifecycle/Lifecycle$Event.class", "size": 3618, "crc": 1794543790}, {"key": "androidx/lifecycle/Lifecycle$State.class", "name": "androidx/lifecycle/Lifecycle$State.class", "size": 2085, "crc": 1927118992}, {"key": "androidx/lifecycle/Lifecycle.class", "name": "androidx/lifecycle/Lifecycle.class", "size": 4780, "crc": 1741072523}, {"key": "androidx/lifecycle/LifecycleController.class", "name": "androidx/lifecycle/LifecycleController.class", "size": 4658, "crc": -1761630099}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenCreated$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenCreated$1.class", "size": 4118, "crc": 1210450620}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenResumed$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenResumed$1.class", "size": 4118, "crc": 213152543}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenStarted$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenStarted$1.class", "size": 4118, "crc": -123055868}, {"key": "androidx/lifecycle/LifecycleCoroutineScope.class", "name": "androidx/lifecycle/LifecycleCoroutineScope.class", "size": 3522, "crc": 759479583}, {"key": "androidx/lifecycle/LifecycleCoroutineScopeImpl$register$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScopeImpl$register$1.class", "size": 4156, "crc": 201520443}, {"key": "androidx/lifecycle/LifecycleCoroutineScopeImpl.class", "name": "androidx/lifecycle/LifecycleCoroutineScopeImpl.class", "size": 3622, "crc": -1106754036}, {"key": "androidx/lifecycle/LifecycleEventObserver.class", "name": "androidx/lifecycle/LifecycleEventObserver.class", "size": 968, "crc": -1471782186}, {"key": "androidx/lifecycle/LifecycleKt$eventFlow$1$1.class", "name": "androidx/lifecycle/LifecycleKt$eventFlow$1$1.class", "size": 1486, "crc": 1340393298}, {"key": "androidx/lifecycle/LifecycleKt$eventFlow$1.class", "name": "androidx/lifecycle/LifecycleKt$eventFlow$1.class", "size": 5616, "crc": -1097314852}, {"key": "androidx/lifecycle/LifecycleKt.class", "name": "androidx/lifecycle/LifecycleKt.class", "size": 3142, "crc": 342309099}, {"key": "androidx/lifecycle/LifecycleObserver.class", "name": "androidx/lifecycle/LifecycleObserver.class", "size": 395, "crc": -329931382}, {"key": "androidx/lifecycle/LifecycleOwner.class", "name": "androidx/lifecycle/LifecycleOwner.class", "size": 611, "crc": -1363301969}, {"key": "androidx/lifecycle/LifecycleOwnerKt.class", "name": "androidx/lifecycle/LifecycleOwnerKt.class", "size": 1208, "crc": -317348439}, {"key": "androidx/lifecycle/Lifecycle_jvmKt.class", "name": "androidx/lifecycle/Lifecycle_jvmKt.class", "size": 515, "crc": 2118472692}, {"key": "androidx/lifecycle/Lifecycling.class", "name": "androidx/lifecycle/Lifecycling.class", "size": 9197, "crc": 993309437}, {"key": "androidx/lifecycle/MethodCallsLogger.class", "name": "androidx/lifecycle/MethodCallsLogger.class", "size": 1995, "crc": 1811623749}, {"key": "androidx/lifecycle/PausingDispatcher.class", "name": "androidx/lifecycle/PausingDispatcher.class", "size": 1899, "crc": 1576970027}, {"key": "androidx/lifecycle/PausingDispatcherKt$whenStateAtLeast$2.class", "name": "androidx/lifecycle/PausingDispatcherKt$whenStateAtLeast$2.class", "size": 5702, "crc": -1562974733}, {"key": "androidx/lifecycle/PausingDispatcherKt.class", "name": "androidx/lifecycle/PausingDispatcherKt.class", "size": 6058, "crc": 2006387392}, {"key": "androidx/lifecycle/SingleGeneratedAdapterObserver.class", "name": "androidx/lifecycle/SingleGeneratedAdapterObserver.class", "size": 1796, "crc": -1202315822}, {"key": "androidx/lifecycle/ClassesInfoCache$CallbackInfo.class", "name": "androidx/lifecycle/ClassesInfoCache$CallbackInfo.class", "size": 3345, "crc": -313150915}, {"key": "androidx/lifecycle/ClassesInfoCache$MethodReference.class", "name": "androidx/lifecycle/ClassesInfoCache$MethodReference.class", "size": 2284, "crc": 508395143}, {"key": "androidx/lifecycle/ClassesInfoCache.class", "name": "androidx/lifecycle/ClassesInfoCache.class", "size": 7417, "crc": 1890982937}, {"key": "androidx/lifecycle/GenericLifecycleObserver.class", "name": "androidx/lifecycle/GenericLifecycleObserver.class", "size": 557, "crc": 1876517087}, {"key": "androidx/lifecycle/OnLifecycleEvent.class", "name": "androidx/lifecycle/OnLifecycleEvent.class", "size": 614, "crc": -662081732}, {"key": "androidx/lifecycle/ReflectiveGenericLifecycleObserver.class", "name": "androidx/lifecycle/ReflectiveGenericLifecycleObserver.class", "size": 1628, "crc": -661784695}]