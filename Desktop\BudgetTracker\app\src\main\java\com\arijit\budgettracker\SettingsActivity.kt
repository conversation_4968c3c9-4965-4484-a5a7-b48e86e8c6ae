package com.arijit.budgettracker

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.widget.ImageView
import androidx.appcompat.widget.SwitchCompat
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.cardview.widget.CardView
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.arijit.budgettracker.utils.CurrencyPrefs
import com.arijit.budgettracker.utils.Vibration
import com.google.android.material.bottomsheet.BottomSheetDialog

class SettingsActivity : AppCompatActivity() {

    private lateinit var currency: CardView
    private lateinit var darkModeSwitch: SwitchCompat
    private lateinit var sharedPrefs: SharedPreferences
    private var currencySelected = "₹"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_settings)

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }

        // Initialize SharedPreferences for dark mode
        sharedPrefs = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)

        // 🔙 Back button in header
        val backButton = findViewById<ImageView>(R.id.iv_back)
        backButton.setOnClickListener {
            Vibration.vibrate(this, 50)
            onBackPressedDispatcher.onBackPressed()
        }

        // 💰 Currency card logic
        currency = findViewById(R.id.currency)
        currencySelected = CurrencyPrefs.getSymbol(this)
        findViewById<TextView>(R.id.curr).text = currencySelected

        currency.setOnClickListener {
            Vibration.vibrate(this, 50)
            val bottomSheet = BottomSheetDialog(this)
            val view = layoutInflater.inflate(R.layout.currency_layout, null)
            bottomSheet.setContentView(view)
            bottomSheet.show()

            val categoriesMap = mapOf(
                R.id.inr to "₹",
                R.id.usd to "$",
                R.id.cny to "¥",
                R.id.jpy to "¥",
                R.id.rub to "₽",
                R.id.eur to "€"
            )

            for ((viewId, symbol) in categoriesMap) {
                view.findViewById<TextView>(viewId).setOnClickListener {
                    Vibration.vibrate(this, 50)
                    currencySelected = symbol
                    CurrencyPrefs.setSymbol(this, currencySelected)
                    bottomSheet.dismiss()
                    findViewById<TextView>(R.id.curr).text = currencySelected
                }
            }
        }

        // 🌙 Dark Mode Toggle Logic
        darkModeSwitch = findViewById(R.id.dark_mode_switch)

        // Set initial state based on current theme
        val isDarkMode = sharedPrefs.getBoolean("dark_mode", false)
        darkModeSwitch.isChecked = isDarkMode

        darkModeSwitch.setOnCheckedChangeListener { _, isChecked ->
            Vibration.vibrate(this, 50)

            // Save preference
            sharedPrefs.edit().putBoolean("dark_mode", isChecked).apply()

            // Apply theme
            if (isChecked) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
            }
        }
    }
}
