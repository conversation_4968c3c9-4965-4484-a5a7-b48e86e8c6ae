<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:scrollbars="none"
    android:fillViewport="true"
    tools:context=".fragments.StatsFragment">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/last_seven_days_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="24dp"
            android:fontFamily="@font/montserrat_bold"
            android:text="Last 7 days"
            android:textColor="@color/light_green"
            android:textSize="26sp" />

        <androidx.cardview.widget.CardView
            android:id="@+id/daily_expense_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/last_seven_days_txt"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="10dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="0dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/light_green">

                <com.github.mikephil.charting.charts.BarChart
                    android:id="@+id/daily_expense"
                    android:layout_width="match_parent"
                    android:layout_height="300dp" />

            </RelativeLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/most_spent_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/daily_expense_card"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="10dp"
            app:cardCornerRadius="20dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/lime"
                android:orientation="vertical"
                android:paddingHorizontal="20dp"
                android:paddingVertical="15dp">

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@id/fire"
                    android:fontFamily="@font/montserrat_regular"
                    android:text="Most spent this month on"
                    android:textColor="@color/dark_green"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/most_spent_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/title"
                    android:fontFamily="@font/montserrat_bold"
                    android:includeFontPadding="false"
                    android:text="3rd Aug - ₹1,394"
                    android:textColor="@color/dark_green"
                    android:textSize="25sp" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/fire"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_alignParentStart="true"
                    android:layout_marginStart="-4dp"
                    android:layout_marginTop="-3dp"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/fire_lottie" />

            </RelativeLayout>

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/catg_wise_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/most_spent_card"
            android:layout_marginHorizontal="23dp"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/montserrat_bold"
            android:text="Category wise breakdown"
            android:textColor="@color/light_green"
            android:textSize="18sp" />

        <LinearLayout
            android:id="@+id/pie_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/catg_wise_txt"
            android:layout_marginHorizontal="22dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/chart_bg"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="20dp">

            <com.github.mikephil.charting.charts.PieChart
                android:id="@+id/pie_chart"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:layout_marginBottom="20dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/savings_wise_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/pie_layout"
            android:layout_marginHorizontal="23dp"
            android:layout_marginTop="20dp"
            android:fontFamily="@font/montserrat_bold"
            android:text="Savings breakdown"
            android:textColor="@color/light_green"
            android:textSize="18sp" />

        <LinearLayout
            android:id="@+id/savings_pie_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/savings_wise_txt"
            android:layout_marginHorizontal="22dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/chart_bg"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:padding="20dp">

            <com.github.mikephil.charting.charts.PieChart
                android:id="@+id/savings_pie_chart"
                android:layout_width="300dp"
                android:layout_height="300dp"
                android:layout_marginBottom="20dp" />

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:layout_below="@id/savings_pie_layout" />

    </RelativeLayout>

</ScrollView>