1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.imagedownloader"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permission for downloading images -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Storage permissions for saving images -->
15    <uses-permission
15-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:12:9-35
21
22    <!-- For Android 13+ (API 33+) -->
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:15:5-76
23-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:15:22-73
24
25    <permission
25-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
26        android:name="com.example.imagedownloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.example.imagedownloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
30
31    <application
31-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:17:5-37:19
32        android:allowBackup="true"
32-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:18:9-35
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\273013bf131dc793dae0cb2367ffa634\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
34        android:dataExtractionRules="@xml/data_extraction_rules"
34-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:19:9-65
35        android:debuggable="true"
36        android:extractNativeLibs="false"
37        android:fullBackupContent="@xml/backup_rules"
37-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:20:9-54
38        android:icon="@mipmap/ic_launcher"
38-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:21:9-43
39        android:label="@string/app_name"
39-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:22:9-41
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:23:9-54
41        android:supportsRtl="true"
41-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:24:9-35
42        android:testOnly="true"
43        android:theme="@style/Theme.ImageDownloader"
43-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:25:9-53
44        android:usesCleartextTraffic="true" >
44-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:26:9-44
45        <activity
45-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:27:9-36:20
46            android:name="com.example.imagedownloader.MainActivity"
46-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:28:13-41
47            android:exported="true"
47-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:29:13-36
48            android:label="@string/app_name"
48-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:30:13-45
49            android:theme="@style/Theme.ImageDownloader" >
49-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:31:13-57
50            <intent-filter>
50-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:32:13-35:29
51                <action android:name="android.intent.action.MAIN" />
51-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:33:17-69
51-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:33:25-66
52
53                <category android:name="android.intent.category.LAUNCHER" />
53-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:34:17-77
53-->C:\Users\<USER>\Desktop\IMAGE_DOWNLOADER_android\app\src\main\AndroidManifest.xml:34:27-74
54            </intent-filter>
55        </activity>
56        <activity
56-->[androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
57            android:name="androidx.compose.ui.tooling.PreviewActivity"
57-->[androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
58            android:exported="true" />
58-->[androidx.compose.ui:ui-tooling-android:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b70dfe2cea81ffe04626966bee0401a\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
59        <activity
59-->[androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:23:9-25:39
60            android:name="androidx.activity.ComponentActivity"
60-->[androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:24:13-63
61            android:exported="true" />
61-->[androidx.compose.ui:ui-test-manifest:1.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\bea7631352c25d32ea1ff4a06caf140b\transformed\ui-test-manifest-1.7.5\AndroidManifest.xml:25:13-36
62
63        <provider
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
64            android:name="androidx.startup.InitializationProvider"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
65            android:authorities="com.example.imagedownloader.androidx-startup"
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
66            android:exported="false" >
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
67            <meta-data
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.emoji2.text.EmojiCompatInitializer"
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
69                android:value="androidx.startup" />
69-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f7d91b827dd14bdab50394bca29157\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
70            <meta-data
70-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
71-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
72                android:value="androidx.startup" />
72-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b4400e438f8b48481c3c54a4e8bbde0\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
75                android:value="androidx.startup" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
76        </provider>
77
78        <receiver
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
79            android:name="androidx.profileinstaller.ProfileInstallReceiver"
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
80            android:directBootAware="false"
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
81            android:enabled="true"
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
82            android:exported="true"
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
83            android:permission="android.permission.DUMP" >
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
85                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
88                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
91                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
94                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e42c1e2083e6a0a18f38d5428613131\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
95            </intent-filter>
96        </receiver>
97    </application>
98
99</manifest>
