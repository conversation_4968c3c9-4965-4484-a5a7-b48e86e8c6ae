[{"key": "androidx/activity/compose/ActivityComposeUtilsKt.class", "name": "androidx/activity/compose/ActivityComposeUtilsKt.class", "size": 1046, "crc": 467388989}, {"key": "androidx/activity/compose/ActivityResultLauncherHolder.class", "name": "androidx/activity/compose/ActivityResultLauncherHolder.class", "size": 2462, "crc": 1884005465}, {"key": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1.class", "size": 2377, "crc": 595459979}, {"key": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1.class", "name": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1.class", "size": 5508, "crc": -651732193}, {"key": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1.class", "name": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1.class", "size": 1588, "crc": -531707919}, {"key": "androidx/activity/compose/ActivityResultRegistryKt.class", "name": "androidx/activity/compose/ActivityResultRegistryKt.class", "size": 7590, "crc": -700711089}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$1$1.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$1$1.class", "size": 1479, "crc": -596123820}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1.class", "size": 2195, "crc": -1727118253}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$2$1.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$2$1.class", "size": 3423, "crc": -877948432}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$3.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$3.class", "size": 1838, "crc": 1925984074}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$backCallback$1$1.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$backCallback$1$1.class", "size": 1522, "crc": 123387424}, {"key": "androidx/activity/compose/BackHandlerKt.class", "name": "androidx/activity/compose/BackHandlerKt.class", "size": 8553, "crc": -1320049577}, {"key": "androidx/activity/compose/ComponentActivityKt.class", "name": "androidx/activity/compose/ComponentActivityKt.class", "size": 4266, "crc": -707835248}, {"key": "androidx/activity/compose/LocalActivityResultRegistryOwner$LocalComposition$1.class", "name": "androidx/activity/compose/LocalActivityResultRegistryOwner$LocalComposition$1.class", "size": 1352, "crc": 715108056}, {"key": "androidx/activity/compose/LocalActivityResultRegistryOwner.class", "name": "androidx/activity/compose/LocalActivityResultRegistryOwner.class", "size": 5405, "crc": 1142019662}, {"key": "androidx/activity/compose/LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1.class", "name": "androidx/activity/compose/LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1.class", "size": 1320, "crc": 1802299417}, {"key": "androidx/activity/compose/LocalFullyDrawnReporterOwner.class", "name": "androidx/activity/compose/LocalFullyDrawnReporterOwner.class", "size": 5685, "crc": 428694162}, {"key": "androidx/activity/compose/LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1.class", "name": "androidx/activity/compose/LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1.class", "size": 1360, "crc": -114965854}, {"key": "androidx/activity/compose/LocalOnBackPressedDispatcherOwner.class", "name": "androidx/activity/compose/LocalOnBackPressedDispatcherOwner.class", "size": 5766, "crc": 1752420111}, {"key": "androidx/activity/compose/ManagedActivityResultLauncher.class", "name": "androidx/activity/compose/ManagedActivityResultLauncher.class", "size": 3241, "crc": -1569828288}, {"key": "androidx/activity/compose/OnBackInstance$job$1$1.class", "name": "androidx/activity/compose/OnBackInstance$job$1$1.class", "size": 3344, "crc": 351065657}, {"key": "androidx/activity/compose/OnBackInstance$job$1.class", "name": "androidx/activity/compose/OnBackInstance$job$1.class", "size": 5690, "crc": -1011818597}, {"key": "androidx/activity/compose/OnBackInstance.class", "name": "androidx/activity/compose/OnBackInstance.class", "size": 4649, "crc": 1968541243}, {"key": "androidx/activity/compose/PredictiveBackHandlerCallback.class", "name": "androidx/activity/compose/PredictiveBackHandlerCallback.class", "size": 4794, "crc": -211144873}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$2$1.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$2$1.class", "size": 3649, "crc": -144107739}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1.class", "size": 2296, "crc": 1088189129}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$3$1.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$3$1.class", "size": 3532, "crc": 1726420428}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$4.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$4.class", "size": 2148, "crc": 1846596490}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt.class", "size": 12366, "crc": 642059971}, {"key": "androidx/activity/compose/ReportDrawnComposition$checkReporter$1.class", "name": "androidx/activity/compose/ReportDrawnComposition$checkReporter$1.class", "size": 1801, "crc": 616718532}, {"key": "androidx/activity/compose/ReportDrawnComposition$observeReporter$1.class", "name": "androidx/activity/compose/ReportDrawnComposition$observeReporter$1.class", "size": 1718, "crc": -253037839}, {"key": "androidx/activity/compose/ReportDrawnComposition$snapshotStateObserver$1.class", "name": "androidx/activity/compose/ReportDrawnComposition$snapshotStateObserver$1.class", "size": 1688, "crc": -426348463}, {"key": "androidx/activity/compose/ReportDrawnComposition.class", "name": "androidx/activity/compose/ReportDrawnComposition.class", "size": 4154, "crc": 385139534}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawn$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawn$1.class", "size": 1285, "crc": 1462340618}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawn$2.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawn$2.class", "size": 1415, "crc": -422479786}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$1$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$1$1.class", "size": 5121, "crc": -844831841}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$2.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$2.class", "size": 1852, "crc": -65978723}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$fullyDrawnReporter$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$fullyDrawnReporter$1.class", "size": 1890, "crc": 857497705}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1.class", "size": 1915, "crc": -116997982}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2.class", "size": 2171, "crc": -480815930}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1.class", "size": 3618, "crc": -751688202}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$2.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$2.class", "size": 1757, "crc": -180670835}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1.class", "size": 1795, "crc": 1895945797}, {"key": "androidx/activity/compose/ReportDrawnKt.class", "name": "androidx/activity/compose/ReportDrawnKt.class", "size": 7169, "crc": 412795906}, {"key": "META-INF/activity-compose_release.kotlin_module", "name": "META-INF/activity-compose_release.kotlin_module", "size": 180, "crc": 508638569}, {"key": "META-INF/androidx.activity_activity-compose.version", "name": "META-INF/androidx.activity_activity-compose.version", "size": 6, "crc": -1420053471}]