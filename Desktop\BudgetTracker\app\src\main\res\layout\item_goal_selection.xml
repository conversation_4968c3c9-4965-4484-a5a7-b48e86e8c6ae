<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="10dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/light_green"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/goal_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/montserrat_bold"
            android:text="Car Fund"
            android:textColor="@color/dark_green"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp">

            <TextView
                android:id="@+id/goal_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_bold"
                android:text="₹0"
                android:textColor="@color/medium_green"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/goal_target"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:text="/ ₹900"
                android:textColor="@color/medium_green"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
