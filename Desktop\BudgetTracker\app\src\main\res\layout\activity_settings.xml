<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:background="@color/theme_background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".SettingsActivity">

    <!-- Header / App Bar -->
    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:padding="4dp"
            android:src="@drawable/ic_arrow_back"
        android:contentDescription="Back"
        app:tint="@color/light_green" />

        <!-- Title + Subtitle in a column -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="12dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_bold"
                android:text="About"
                android:includeFontPadding="false"
                android:textColor="@color/light_green"
                android:textSize="28sp" />

            <TextView
                android:id="@+id/tv_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Customize your app experience"
                android:textColor="@color/light_green"
                android:includeFontPadding="false"
                android:fontFamily="@font/montserrat_regular"
                android:textSize="12sp"
                android:alpha="0.8" />

        </LinearLayout>

    </LinearLayout>

    <!-- Content -->
    <LinearLayout
        android:id="@+id/horizontal_cards"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_below="@id/header"
        android:orientation="vertical"
        android:layout_marginHorizontal="20dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/currency"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:cardCornerRadius="18dp"
            app:cardUseCompatPadding="true"
            app:cardElevation="4dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/theme_surface"
                android:orientation="horizontal"
                android:paddingVertical="18dp"
                android:paddingHorizontal="16dp">

                <ImageView
                    android:id="@+id/icon"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_centerVertical="true"
                    android:src="@drawable/note"
                    app:tint="@color/dark_green" />

                <LinearLayout
                    android:id="@+id/text_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@id/icon"
                    android:layout_toStartOf="@id/curr"
                    android:layout_marginStart="16dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/curr_txt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Change currency"
                        android:textColor="@color/theme_text_secondary"
                        android:includeFontPadding="false"
                        android:fontFamily="@font/montserrat_bold"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/curr_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Set your preferred currency symbol"
                        android:textColor="@color/theme_text_secondary"
                        android:includeFontPadding="false"
                        android:fontFamily="@font/montserrat_regular"
                        android:textSize="12sp"
                        android:alpha="0.8" />

                </LinearLayout>

                <TextView
                    android:id="@+id/curr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="₹"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:textColor="@color/theme_text_secondary"
                    android:includeFontPadding="false"
                    android:fontFamily="@font/montserrat_bold"
                    android:textSize="18sp" />

            </RelativeLayout>

        </androidx.cardview.widget.CardView>

        <!-- Dark Mode Toggle Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/dark_mode_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:cardCornerRadius="18dp"
            app:cardUseCompatPadding="true"
            app:cardElevation="4dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/theme_surface"
                android:orientation="horizontal"
                android:paddingVertical="18dp"
                android:paddingHorizontal="16dp">

                <ImageView
                    android:id="@+id/dark_mode_icon"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ic_dark_mode"
                    app:tint="@color/theme_text_secondary" />

                <LinearLayout
                    android:id="@+id/dark_mode_text_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@id/dark_mode_icon"
                    android:layout_toStartOf="@id/dark_mode_switch"
                    android:layout_marginStart="16dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/dark_mode_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Dark Mode"
                        android:textColor="@color/theme_text_secondary"
                        android:includeFontPadding="false"
                        android:fontFamily="@font/montserrat_bold"
                        android:textSize="16sp" />

                    <TextView
                        android:id="@+id/dark_mode_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Switch between light and dark theme"
                        android:textColor="@color/theme_text_secondary"
                        android:includeFontPadding="false"
                        android:fontFamily="@font/montserrat_regular"
                        android:textSize="12sp"
                        android:alpha="0.8" />

                </LinearLayout>

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/dark_mode_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    app:thumbTint="@color/dark_green"
                    app:trackTint="@color/medium_green" />

            </RelativeLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</RelativeLayout>
