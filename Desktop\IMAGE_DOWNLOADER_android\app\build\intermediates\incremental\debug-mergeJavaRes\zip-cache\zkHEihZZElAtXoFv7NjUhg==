[{"key": "androidx/compose/material/ripple/AndroidRippleIndicationInstance$onInvalidateRipple$1.class", "name": "androidx/compose/material/ripple/AndroidRippleIndicationInstance$onInvalidateRipple$1.class", "size": 1681, "crc": 819223873}, {"key": "androidx/compose/material/ripple/AndroidRippleIndicationInstance.class", "name": "androidx/compose/material/ripple/AndroidRippleIndicationInstance.class", "size": 12349, "crc": 1577844037}, {"key": "androidx/compose/material/ripple/AndroidRippleNode$addRipple$1$1$1.class", "name": "androidx/compose/material/ripple/AndroidRippleNode$addRipple$1$1$1.class", "size": 1638, "crc": -1380420031}, {"key": "androidx/compose/material/ripple/AndroidRippleNode.class", "name": "androidx/compose/material/ripple/AndroidRippleNode.class", "size": 8680, "crc": 1838926907}, {"key": "androidx/compose/material/ripple/CommonRipple.class", "name": "androidx/compose/material/ripple/CommonRipple.class", "size": 5682, "crc": 1599377978}, {"key": "androidx/compose/material/ripple/CommonRippleIndicationInstance$addRipple$2.class", "name": "androidx/compose/material/ripple/CommonRippleIndicationInstance$addRipple$2.class", "size": 4695, "crc": -1602761408}, {"key": "androidx/compose/material/ripple/CommonRippleIndicationInstance.class", "name": "androidx/compose/material/ripple/CommonRippleIndicationInstance.class", "size": 9313, "crc": -1806686901}, {"key": "androidx/compose/material/ripple/CommonRippleNode$addRipple$2.class", "name": "androidx/compose/material/ripple/CommonRippleNode$addRipple$2.class", "size": 4716, "crc": -1209407784}, {"key": "androidx/compose/material/ripple/CommonRippleNode.class", "name": "androidx/compose/material/ripple/CommonRippleNode.class", "size": 9716, "crc": -1374422894}, {"key": "androidx/compose/material/ripple/DebugRippleTheme.class", "name": "androidx/compose/material/ripple/DebugRippleTheme.class", "size": 3098, "crc": -1414102673}, {"key": "androidx/compose/material/ripple/PlatformRipple.class", "name": "androidx/compose/material/ripple/PlatformRipple.class", "size": 6722, "crc": -512537344}, {"key": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1$1.class", "name": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1$1.class", "size": 3157, "crc": -2137315528}, {"key": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1.class", "name": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1.class", "size": 4434, "crc": 622028418}, {"key": "androidx/compose/material/ripple/Ripple.class", "name": "androidx/compose/material/ripple/Ripple.class", "size": 9213, "crc": -590914693}, {"key": "androidx/compose/material/ripple/RippleAlpha.class", "name": "androidx/compose/material/ripple/RippleAlpha.class", "size": 2585, "crc": -1132594698}, {"key": "androidx/compose/material/ripple/RippleAnimation$animate$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$animate$1.class", "size": 1774, "crc": 1274228871}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$1.class", "size": 4382, "crc": 868608265}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$2.class", "size": 4398, "crc": 458947532}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$3.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$3.class", "size": 4391, "crc": -346912476}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2.class", "size": 4038, "crc": -263525622}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2$1.class", "size": 4388, "crc": 440780828}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2.class", "size": 3830, "crc": -2126375460}, {"key": "androidx/compose/material/ripple/RippleAnimation.class", "name": "androidx/compose/material/ripple/RippleAnimation.class", "size": 12335, "crc": -1212871902}, {"key": "androidx/compose/material/ripple/RippleAnimationKt.class", "name": "androidx/compose/material/ripple/RippleAnimationKt.class", "size": 2783, "crc": 131876428}, {"key": "androidx/compose/material/ripple/RippleContainer.class", "name": "androidx/compose/material/ripple/RippleContainer.class", "size": 5285, "crc": -689906216}, {"key": "androidx/compose/material/ripple/RippleHostKey.class", "name": "androidx/compose/material/ripple/RippleHostKey.class", "size": 489, "crc": -544676383}, {"key": "androidx/compose/material/ripple/RippleHostMap.class", "name": "androidx/compose/material/ripple/RippleHostMap.class", "size": 3238, "crc": 1089314071}, {"key": "androidx/compose/material/ripple/RippleHostView$Companion.class", "name": "androidx/compose/material/ripple/RippleHostView$Companion.class", "size": 1065, "crc": -1740492451}, {"key": "androidx/compose/material/ripple/RippleHostView.class", "name": "androidx/compose/material/ripple/RippleHostView.class", "size": 7934, "crc": -1802645722}, {"key": "androidx/compose/material/ripple/RippleIndicationInstance$stateLayer$1.class", "name": "androidx/compose/material/ripple/RippleIndicationInstance$stateLayer$1.class", "size": 1669, "crc": -206091166}, {"key": "androidx/compose/material/ripple/RippleIndicationInstance.class", "name": "androidx/compose/material/ripple/RippleIndicationInstance.class", "size": 4806, "crc": -1498224266}, {"key": "androidx/compose/material/ripple/RippleKt.class", "name": "androidx/compose/material/ripple/RippleKt.class", "size": 8107, "crc": 181659727}, {"key": "androidx/compose/material/ripple/RippleNode$onAttach$1$1.class", "name": "androidx/compose/material/ripple/RippleNode$onAttach$1$1.class", "size": 3775, "crc": -1035504441}, {"key": "androidx/compose/material/ripple/RippleNode$onAttach$1.class", "name": "androidx/compose/material/ripple/RippleNode$onAttach$1.class", "size": 4077, "crc": 2140790233}, {"key": "androidx/compose/material/ripple/RippleNode.class", "name": "androidx/compose/material/ripple/RippleNode.class", "size": 11831, "crc": 45278254}, {"key": "androidx/compose/material/ripple/RippleTheme$Companion.class", "name": "androidx/compose/material/ripple/RippleTheme$Companion.class", "size": 3255, "crc": 1301546537}, {"key": "androidx/compose/material/ripple/RippleTheme.class", "name": "androidx/compose/material/ripple/RippleTheme.class", "size": 1762, "crc": 1159792567}, {"key": "androidx/compose/material/ripple/RippleThemeKt$LocalRippleTheme$1.class", "name": "androidx/compose/material/ripple/RippleThemeKt$LocalRippleTheme$1.class", "size": 1452, "crc": 1806597910}, {"key": "androidx/compose/material/ripple/RippleThemeKt.class", "name": "androidx/compose/material/ripple/RippleThemeKt.class", "size": 2947, "crc": 1526051443}, {"key": "androidx/compose/material/ripple/Ripple_androidKt.class", "name": "androidx/compose/material/ripple/Ripple_androidKt.class", "size": 4668, "crc": -1938020888}, {"key": "androidx/compose/material/ripple/StateLayer$handleInteraction$1.class", "name": "androidx/compose/material/ripple/StateLayer$handleInteraction$1.class", "size": 4420, "crc": 1664172989}, {"key": "androidx/compose/material/ripple/StateLayer$handleInteraction$2.class", "name": "androidx/compose/material/ripple/StateLayer$handleInteraction$2.class", "size": 4358, "crc": 315627144}, {"key": "androidx/compose/material/ripple/StateLayer.class", "name": "androidx/compose/material/ripple/StateLayer.class", "size": 9848, "crc": 1865132568}, {"key": "androidx/compose/material/ripple/UnprojectedRipple$Companion.class", "name": "androidx/compose/material/ripple/UnprojectedRipple$Companion.class", "size": 1015, "crc": -744191215}, {"key": "androidx/compose/material/ripple/UnprojectedRipple$MRadiusHelper.class", "name": "androidx/compose/material/ripple/UnprojectedRipple$MRadiusHelper.class", "size": 1374, "crc": 1162597553}, {"key": "androidx/compose/material/ripple/UnprojectedRipple.class", "name": "androidx/compose/material/ripple/UnprojectedRipple.class", "size": 4503, "crc": -1258162363}, {"key": "META-INF/androidx.compose.material_material-ripple.version", "name": "META-INF/androidx.compose.material_material-ripple.version", "size": 6, "crc": 501114004}, {"key": "META-INF/material-ripple_release.kotlin_module", "name": "META-INF/material-ripple_release.kotlin_module", "size": 122, "crc": -775530715}]