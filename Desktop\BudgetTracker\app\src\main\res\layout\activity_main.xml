<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/theme_background"
    tools:context=".MainActivity">

    <!-- Header row -->
    <LinearLayout
        android:id="@+id/header_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <!-- Screen title -->
        <TextView
            android:id="@+id/header_txt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/montserrat_bold"
            android:text="Home"
            android:includeFontPadding="false"
            android:textColor="@color/light_green"
            android:textSize="28sp" />

        <!-- Settings icon -->
        <ImageView
            android:id="@+id/settings"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/settings"
        android:contentDescription="Settings"
        app:tint="@color/light_green" />
    </LinearLayout>

    <!-- ViewPager: main content -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@id/header_container"
        android:layout_above="@id/tab_layout"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="8dp" />

    <!-- Bottom Tab Bar -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/theme_background"
        app:tabIndicatorFullWidth="false"
        app:tabIndicatorGravity="center"
        app:tabRippleColor="@android:color/transparent"
        app:tabSelectedTextColor="@color/light_green"
        app:tabTextColor="@color/light_green" />

</RelativeLayout>
