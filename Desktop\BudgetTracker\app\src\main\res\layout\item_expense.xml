<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="16dp"
    android:paddingStart="15dp"
    android:paddingEnd="20dp"
    android:layout_marginBottom="10dp"
    android:background="@drawable/expense_bg">

    <TextView
        android:id="@+id/textViewAmount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/dark_green"
        android:text="₹500"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:fontFamily="@font/montserrat_bold"
        android:textSize="20sp" />

    <ImageView
        android:id="@+id/catg_img"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:src="@drawable/health" />

    <TextView
        android:id="@+id/textViewCategory"
        android:layout_width="0dp"
        android:fontFamily="@font/montserrat_bold"
        android:textColor="@color/dark_green"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:textSize="18sp"
        android:includeFontPadding="false"
        android:layout_toEndOf="@id/catg_img"
        android:layout_toStartOf="@id/textViewAmount"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="Food" />

</RelativeLayout>
