[{"key": "com/google/accompanist/drawablepainter/DrawablePainter$WhenMappings.class", "name": "com/google/accompanist/drawablepainter/DrawablePainter$WhenMappings.class", "size": 836, "crc": -1467220023}, {"key": "com/google/accompanist/drawablepainter/DrawablePainter$callback$2$1.class", "name": "com/google/accompanist/drawablepainter/DrawablePainter$callback$2$1.class", "size": 2943, "crc": -1273244685}, {"key": "com/google/accompanist/drawablepainter/DrawablePainter$callback$2.class", "name": "com/google/accompanist/drawablepainter/DrawablePainter$callback$2.class", "size": 1522, "crc": 916668938}, {"key": "com/google/accompanist/drawablepainter/DrawablePainter.class", "name": "com/google/accompanist/drawablepainter/DrawablePainter.class", "size": 10298, "crc": 1293918521}, {"key": "com/google/accompanist/drawablepainter/DrawablePainterKt$MAIN_HANDLER$2.class", "name": "com/google/accompanist/drawablepainter/DrawablePainterKt$MAIN_HANDLER$2.class", "size": 1367, "crc": 1757903915}, {"key": "com/google/accompanist/drawablepainter/DrawablePainterKt.class", "name": "com/google/accompanist/drawablepainter/DrawablePainterKt.class", "size": 5936, "crc": -1112810830}, {"key": "com/google/accompanist/drawablepainter/EmptyPainter.class", "name": "com/google/accompanist/drawablepainter/EmptyPainter.class", "size": 1671, "crc": -809538751}, {"key": "META-INF/drawablepainter_release.kotlin_module", "name": "META-INF/drawablepainter_release.kotlin_module", "size": 85, "crc": 1388668531}]