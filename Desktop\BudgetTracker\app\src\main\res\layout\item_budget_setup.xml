<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="15dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/light_green"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/category_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/montserrat_bold"
            android:text="Food"
            android:textColor="@color/dark_green"
            android:textSize="16sp" />

        <EditText
            android:id="@+id/amount_edit"
            android:layout_width="120dp"
            android:layout_height="wrap_content"
            android:background="@android:color/transparent"
            android:fontFamily="@font/montserrat_regular"
            android:gravity="end"
            android:hint="Amount"
            android:inputType="numberDecimal"
            android:textColor="@color/medium_green"
            android:textColorHint="@color/medium_green"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
