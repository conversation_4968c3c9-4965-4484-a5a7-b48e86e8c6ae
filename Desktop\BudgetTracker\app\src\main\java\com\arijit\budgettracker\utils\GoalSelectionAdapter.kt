package com.arijit.budgettracker.utils

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.arijit.budgettracker.R
import com.arijit.budgettracker.db.SavingsGoal
import java.text.SimpleDateFormat
import java.util.Locale

class GoalSelectionAdapter(
    private val goals: List<SavingsGoal>,
    private val onGoalClick: (SavingsGoal) -> Unit
) : RecyclerView.Adapter<GoalSelectionAdapter.GoalViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GoalViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_goal_selection, parent, false)
        return GoalViewHolder(view)
    }

    override fun onBindViewHolder(holder: GoalViewHolder, position: Int) {
        holder.bind(goals[position])
    }

    override fun getItemCount(): Int = goals.size

    inner class GoalViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val titleText: TextView = itemView.findViewById(R.id.goal_title)
        private val progressText: TextView = itemView.findViewById(R.id.goal_progress)
        private val targetText: TextView = itemView.findViewById(R.id.goal_target)

        fun bind(goal: SavingsGoal) {
            titleText.text = goal.title
            progressText.text = "₹${goal.currentAmount.toInt()}"
            targetText.text = "/ ₹${goal.targetAmount.toInt()}"
            
            itemView.setOnClickListener {
                onGoalClick(goal)
            }
        }
    }
}
