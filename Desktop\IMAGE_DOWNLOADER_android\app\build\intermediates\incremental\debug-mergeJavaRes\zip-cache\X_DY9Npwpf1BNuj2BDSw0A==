[{"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$1.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$1.class", "size": 1931, "crc": -1640808297}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$2.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$2.class", "size": 1977, "crc": -1225768483}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$3.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$3.class", "size": 1963, "crc": 460228250}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$4.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$4.class", "size": 2482, "crc": -542943736}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$1.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$1.class", "size": 2151, "crc": -318782002}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$2.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$2.class", "size": 2183, "crc": 1495941665}, {"key": "androidx/activity/ActivityViewModelLazyKt.class", "name": "androidx/activity/ActivityViewModelLazyKt.class", "size": 4396, "crc": -1784820198}, {"key": "androidx/activity/Api26Impl.class", "name": "androidx/activity/Api26Impl.class", "size": 1654, "crc": -1947708465}, {"key": "androidx/activity/Api34Impl.class", "name": "androidx/activity/Api34Impl.class", "size": 1990, "crc": -61264803}, {"key": "androidx/activity/BackEventCompat$Companion.class", "name": "androidx/activity/BackEventCompat$Companion.class", "size": 892, "crc": -358212213}, {"key": "androidx/activity/BackEventCompat$SwipeEdge.class", "name": "androidx/activity/BackEventCompat$SwipeEdge.class", "size": 1134, "crc": -662310490}, {"key": "androidx/activity/BackEventCompat.class", "name": "androidx/activity/BackEventCompat.class", "size": 3536, "crc": 1190707418}, {"key": "androidx/activity/Cancellable.class", "name": "androidx/activity/Cancellable.class", "size": 421, "crc": -708940838}, {"key": "androidx/activity/ComponentActivity$4.class", "name": "androidx/activity/ComponentActivity$4.class", "size": 1739, "crc": -420230451}, {"key": "androidx/activity/ComponentActivity$Api33Impl.class", "name": "androidx/activity/ComponentActivity$Api33Impl.class", "size": 1546, "crc": 1135160625}, {"key": "androidx/activity/ComponentActivity$Companion.class", "name": "androidx/activity/ComponentActivity$Companion.class", "size": 874, "crc": 797283997}, {"key": "androidx/activity/ComponentActivity$NonConfigurationInstances.class", "name": "androidx/activity/ComponentActivity$NonConfigurationInstances.class", "size": 1556, "crc": 1505560637}, {"key": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutor.class", "name": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutor.class", "size": 870, "crc": **********}, {"key": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutorImpl.class", "name": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutorImpl.class", "size": 4920, "crc": -695644619}, {"key": "androidx/activity/ComponentActivity$activityResultRegistry$1.class", "name": "androidx/activity/ComponentActivity$activityResultRegistry$1.class", "size": 7012, "crc": -**********}, {"key": "androidx/activity/ComponentActivity$defaultViewModelProviderFactory$2.class", "name": "androidx/activity/ComponentActivity$defaultViewModelProviderFactory$2.class", "size": 1909, "crc": -**********}, {"key": "androidx/activity/ComponentActivity$fullyDrawnReporter$2$1.class", "name": "androidx/activity/ComponentActivity$fullyDrawnReporter$2$1.class", "size": 1313, "crc": **********}, {"key": "androidx/activity/ComponentActivity$fullyDrawnReporter$2.class", "name": "androidx/activity/ComponentActivity$fullyDrawnReporter$2.class", "size": 1820, "crc": -**********}, {"key": "androidx/activity/ComponentActivity$onBackPressedDispatcher$2.class", "name": "androidx/activity/ComponentActivity$onBackPressedDispatcher$2.class", "size": 3564, "crc": **********}, {"key": "androidx/activity/ComponentActivity.class", "name": "androidx/activity/ComponentActivity.class", "size": 39269, "crc": **********}, {"key": "androidx/activity/ComponentDialog.class", "name": "androidx/activity/ComponentDialog.class", "size": 7578, "crc": -**********}, {"key": "androidx/activity/EdgeToEdge.class", "name": "androidx/activity/EdgeToEdge.class", "size": 5229, "crc": -873388881}, {"key": "androidx/activity/EdgeToEdgeApi21.class", "name": "androidx/activity/EdgeToEdgeApi21.class", "size": 1719, "crc": -1861298073}, {"key": "androidx/activity/EdgeToEdgeApi23.class", "name": "androidx/activity/EdgeToEdgeApi23.class", "size": 2154, "crc": -81204071}, {"key": "androidx/activity/EdgeToEdgeApi26.class", "name": "androidx/activity/EdgeToEdgeApi26.class", "size": 2399, "crc": -1401654137}, {"key": "androidx/activity/EdgeToEdgeApi28.class", "name": "androidx/activity/EdgeToEdgeApi28.class", "size": 1410, "crc": 506896715}, {"key": "androidx/activity/EdgeToEdgeApi29.class", "name": "androidx/activity/EdgeToEdgeApi29.class", "size": 2634, "crc": 1647525370}, {"key": "androidx/activity/EdgeToEdgeApi30.class", "name": "androidx/activity/EdgeToEdgeApi30.class", "size": 1410, "crc": -22811235}, {"key": "androidx/activity/EdgeToEdgeBase.class", "name": "androidx/activity/EdgeToEdgeBase.class", "size": 1571, "crc": 972696176}, {"key": "androidx/activity/EdgeToEdgeImpl.class", "name": "androidx/activity/EdgeToEdgeImpl.class", "size": 1036, "crc": 1336037494}, {"key": "androidx/activity/FullyDrawnReporter.class", "name": "androidx/activity/FullyDrawnReporter.class", "size": 6883, "crc": 1058805723}, {"key": "androidx/activity/FullyDrawnReporterKt$reportWhenComplete$1.class", "name": "androidx/activity/FullyDrawnReporterKt$reportWhenComplete$1.class", "size": 2011, "crc": 1693911518}, {"key": "androidx/activity/FullyDrawnReporterKt.class", "name": "androidx/activity/FullyDrawnReporterKt.class", "size": 3103, "crc": -428465878}, {"key": "androidx/activity/FullyDrawnReporterOwner.class", "name": "androidx/activity/FullyDrawnReporterOwner.class", "size": 670, "crc": 339222718}, {"key": "androidx/activity/ImmLeaksCleaner$Cleaner.class", "name": "androidx/activity/ImmLeaksCleaner$Cleaner.class", "size": 1616, "crc": -2064949897}, {"key": "androidx/activity/ImmLeaksCleaner$Companion$cleaner$2.class", "name": "androidx/activity/ImmLeaksCleaner$Companion$cleaner$2.class", "size": 2866, "crc": -1227111391}, {"key": "androidx/activity/ImmLeaksCleaner$Companion.class", "name": "androidx/activity/ImmLeaksCleaner$Companion.class", "size": 1450, "crc": 2030074279}, {"key": "androidx/activity/ImmLeaksCleaner$FailedInitialization.class", "name": "androidx/activity/ImmLeaksCleaner$FailedInitialization.class", "size": 1974, "crc": -2119668364}, {"key": "androidx/activity/ImmLeaksCleaner$ValidCleaner.class", "name": "androidx/activity/ImmLeaksCleaner$ValidCleaner.class", "size": 2898, "crc": -1985390139}, {"key": "androidx/activity/ImmLeaksCleaner.class", "name": "androidx/activity/ImmLeaksCleaner.class", "size": 4138, "crc": -683231182}, {"key": "androidx/activity/OnBackPressedCallback.class", "name": "androidx/activity/OnBackPressedCallback.class", "size": 4631, "crc": 1259570835}, {"key": "androidx/activity/OnBackPressedDispatcher$1.class", "name": "androidx/activity/OnBackPressedDispatcher$1.class", "size": 1793, "crc": 858506618}, {"key": "androidx/activity/OnBackPressedDispatcher$2.class", "name": "androidx/activity/OnBackPressedDispatcher$2.class", "size": 1796, "crc": 912983702}, {"key": "androidx/activity/OnBackPressedDispatcher$3.class", "name": "androidx/activity/OnBackPressedDispatcher$3.class", "size": 1242, "crc": 729229894}, {"key": "androidx/activity/OnBackPressedDispatcher$4.class", "name": "androidx/activity/OnBackPressedDispatcher$4.class", "size": 1251, "crc": -599734795}, {"key": "androidx/activity/OnBackPressedDispatcher$5.class", "name": "androidx/activity/OnBackPressedDispatcher$5.class", "size": 1242, "crc": 657272376}, {"key": "androidx/activity/OnBackPressedDispatcher$Api33Impl.class", "name": "androidx/activity/OnBackPressedDispatcher$Api33Impl.class", "size": 3101, "crc": -2011792798}, {"key": "androidx/activity/OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1.class", "size": 2959, "crc": -2008672857}, {"key": "androidx/activity/OnBackPressedDispatcher$Api34Impl.class", "name": "androidx/activity/OnBackPressedDispatcher$Api34Impl.class", "size": 2569, "crc": -1353833463}, {"key": "androidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable.class", "name": "androidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable.class", "size": 3083, "crc": 165265846}, {"key": "androidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable.class", "name": "androidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable.class", "size": 2502, "crc": 463905704}, {"key": "androidx/activity/OnBackPressedDispatcher$addCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$addCallback$1.class", "size": 1391, "crc": -122303386}, {"key": "androidx/activity/OnBackPressedDispatcher$addCancellableCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$addCancellableCallback$1.class", "size": 1436, "crc": 1325878599}, {"key": "androidx/activity/OnBackPressedDispatcher.class", "name": "androidx/activity/OnBackPressedDispatcher.class", "size": 13164, "crc": -1064152180}, {"key": "androidx/activity/OnBackPressedDispatcherKt$addCallback$callback$1.class", "name": "androidx/activity/OnBackPressedDispatcherKt$addCallback$callback$1.class", "size": 1519, "crc": 310623790}, {"key": "androidx/activity/OnBackPressedDispatcherKt.class", "name": "androidx/activity/OnBackPressedDispatcherKt.class", "size": 2481, "crc": -746442317}, {"key": "androidx/activity/OnBackPressedDispatcherOwner.class", "name": "androidx/activity/OnBackPressedDispatcherOwner.class", "size": 780, "crc": -1780134358}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$2.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$2.class", "size": 2073, "crc": -1500449956}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$1.class", "size": 2639, "crc": -292907574}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1.class", "size": 3195, "crc": 1284861015}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1.class", "size": 7149, "crc": -1852210835}, {"key": "androidx/activity/PipHintTrackerKt.class", "name": "androidx/activity/PipHintTrackerKt.class", "size": 2692, "crc": -1815415906}, {"key": "androidx/activity/SystemBarStyle$Companion$auto$1.class", "name": "androidx/activity/SystemBarStyle$Companion$auto$1.class", "size": 2022, "crc": 1859324407}, {"key": "androidx/activity/SystemBarStyle$Companion$dark$1.class", "name": "androidx/activity/SystemBarStyle$Companion$dark$1.class", "size": 1737, "crc": 2005757652}, {"key": "androidx/activity/SystemBarStyle$Companion$light$1.class", "name": "androidx/activity/SystemBarStyle$Companion$light$1.class", "size": 1741, "crc": 1154515890}, {"key": "androidx/activity/SystemBarStyle$Companion.class", "name": "androidx/activity/SystemBarStyle$Companion.class", "size": 3172, "crc": 2029813958}, {"key": "androidx/activity/SystemBarStyle.class", "name": "androidx/activity/SystemBarStyle.class", "size": 3699, "crc": -1762759932}, {"key": "androidx/activity/ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1.class", "name": "androidx/activity/ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$1.class", "size": 1837, "crc": -1235656802}, {"key": "androidx/activity/ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2.class", "name": "androidx/activity/ViewTreeFullyDrawnReporterOwner$findViewTreeFullyDrawnReporterOwner$2.class", "size": 2002, "crc": 1499649627}, {"key": "androidx/activity/ViewTreeFullyDrawnReporterOwner.class", "name": "androidx/activity/ViewTreeFullyDrawnReporterOwner.class", "size": 2521, "crc": -260827646}, {"key": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$1.class", "name": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$1.class", "size": 1871, "crc": -1858978807}, {"key": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2.class", "name": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner$findViewTreeOnBackPressedDispatcherOwner$2.class", "size": 2081, "crc": 1066734303}, {"key": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner.class", "name": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner.class", "size": 2650, "crc": 1480814886}, {"key": "androidx/activity/contextaware/ContextAware.class", "name": "androidx/activity/contextaware/ContextAware.class", "size": 989, "crc": -731652454}, {"key": "androidx/activity/contextaware/ContextAwareHelper.class", "name": "androidx/activity/contextaware/ContextAwareHelper.class", "size": 2642, "crc": -737783095}, {"key": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$1.class", "name": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$1.class", "size": 2889, "crc": -1657592583}, {"key": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$listener$1.class", "name": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$listener$1.class", "size": 3237, "crc": -977261674}, {"key": "androidx/activity/contextaware/ContextAwareKt.class", "name": "androidx/activity/contextaware/ContextAwareKt.class", "size": 5073, "crc": 40845482}, {"key": "androidx/activity/contextaware/OnContextAvailableListener.class", "name": "androidx/activity/contextaware/OnContextAvailableListener.class", "size": 682, "crc": 1640788270}, {"key": "androidx/activity/result/ActivityResult$Companion$CREATOR$1.class", "name": "androidx/activity/result/ActivityResult$Companion$CREATOR$1.class", "size": 1916, "crc": 781918674}, {"key": "androidx/activity/result/ActivityResult$Companion.class", "name": "androidx/activity/result/ActivityResult$Companion.class", "size": 1498, "crc": 1083210728}, {"key": "androidx/activity/result/ActivityResult.class", "name": "androidx/activity/result/ActivityResult.class", "size": 3504, "crc": -324203662}, {"key": "androidx/activity/result/ActivityResultCallback.class", "name": "androidx/activity/result/ActivityResultCallback.class", "size": 628, "crc": 1970847939}, {"key": "androidx/activity/result/ActivityResultCaller.class", "name": "androidx/activity/result/ActivityResultCaller.class", "size": 1976, "crc": -1122410830}, {"key": "androidx/activity/result/ActivityResultCallerKt.class", "name": "androidx/activity/result/ActivityResultCallerKt.class", "size": 4465, "crc": -1768547991}, {"key": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2$1.class", "name": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2$1.class", "size": 2729, "crc": 1084971498}, {"key": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2.class", "name": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2.class", "size": 1931, "crc": 1985086368}, {"key": "androidx/activity/result/ActivityResultCallerLauncher.class", "name": "androidx/activity/result/ActivityResultCallerLauncher.class", "size": 4061, "crc": -1276556932}, {"key": "androidx/activity/result/ActivityResultKt.class", "name": "androidx/activity/result/ActivityResultKt.class", "size": 1252, "crc": -1320611944}, {"key": "androidx/activity/result/ActivityResultLauncher.class", "name": "androidx/activity/result/ActivityResultLauncher.class", "size": 1623, "crc": -432064838}, {"key": "androidx/activity/result/ActivityResultLauncherKt.class", "name": "androidx/activity/result/ActivityResultLauncherKt.class", "size": 2023, "crc": 1093049900}, {"key": "androidx/activity/result/ActivityResultRegistry$CallbackAndContract.class", "name": "androidx/activity/result/ActivityResultRegistry$CallbackAndContract.class", "size": 2225, "crc": 455773501}, {"key": "androidx/activity/result/ActivityResultRegistry$Companion.class", "name": "androidx/activity/result/ActivityResultRegistry$Companion.class", "size": 1214, "crc": -610986503}, {"key": "androidx/activity/result/ActivityResultRegistry$LifecycleContainer.class", "name": "androidx/activity/result/ActivityResultRegistry$LifecycleContainer.class", "size": 3491, "crc": 1523179344}, {"key": "androidx/activity/result/ActivityResultRegistry$generateRandomNumber$1.class", "name": "androidx/activity/result/ActivityResultRegistry$generateRandomNumber$1.class", "size": 1490, "crc": 1157358130}, {"key": "androidx/activity/result/ActivityResultRegistry$register$2.class", "name": "androidx/activity/result/ActivityResultRegistry$register$2.class", "size": 4284, "crc": -789259953}, {"key": "androidx/activity/result/ActivityResultRegistry$register$3.class", "name": "androidx/activity/result/ActivityResultRegistry$register$3.class", "size": 4249, "crc": -695057735}, {"key": "androidx/activity/result/ActivityResultRegistry.class", "name": "androidx/activity/result/ActivityResultRegistry.class", "size": 17352, "crc": -192716202}, {"key": "androidx/activity/result/ActivityResultRegistryOwner.class", "name": "androidx/activity/result/ActivityResultRegistryOwner.class", "size": 726, "crc": 1586312733}, {"key": "androidx/activity/result/IntentSenderRequest$Builder$Flag.class", "name": "androidx/activity/result/IntentSenderRequest$Builder$Flag.class", "size": 816, "crc": 812248084}, {"key": "androidx/activity/result/IntentSenderRequest$Builder.class", "name": "androidx/activity/result/IntentSenderRequest$Builder.class", "size": 2552, "crc": 634140502}, {"key": "androidx/activity/result/IntentSenderRequest$Companion$CREATOR$1.class", "name": "androidx/activity/result/IntentSenderRequest$Companion$CREATOR$1.class", "size": 1958, "crc": -1695759292}, {"key": "androidx/activity/result/IntentSenderRequest$Companion.class", "name": "androidx/activity/result/IntentSenderRequest$Companion.class", "size": 1070, "crc": 589185882}, {"key": "androidx/activity/result/IntentSenderRequest.class", "name": "androidx/activity/result/IntentSenderRequest.class", "size": 3926, "crc": 179881267}, {"key": "androidx/activity/result/PickVisualMediaRequest$Builder.class", "name": "androidx/activity/result/PickVisualMediaRequest$Builder.class", "size": 2378, "crc": 548726142}, {"key": "androidx/activity/result/PickVisualMediaRequest.class", "name": "androidx/activity/result/PickVisualMediaRequest.class", "size": 2042, "crc": 1955974580}, {"key": "androidx/activity/result/PickVisualMediaRequestKt.class", "name": "androidx/activity/result/PickVisualMediaRequestKt.class", "size": 2319, "crc": 531241200}, {"key": "androidx/activity/result/contract/ActivityResultContract$SynchronousResult.class", "name": "androidx/activity/result/contract/ActivityResultContract$SynchronousResult.class", "size": 1068, "crc": 1193330250}, {"key": "androidx/activity/result/contract/ActivityResultContract.class", "name": "androidx/activity/result/contract/ActivityResultContract.class", "size": 2153, "crc": 1301119215}, {"key": "androidx/activity/result/contract/ActivityResultContracts$CaptureVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$CaptureVideo.class", "size": 3553, "crc": 715800925}, {"key": "androidx/activity/result/contract/ActivityResultContracts$CreateDocument.class", "name": "androidx/activity/result/contract/ActivityResultContracts$CreateDocument.class", "size": 4968, "crc": -195241468}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetContent.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetContent.class", "size": 4248, "crc": 1128495298}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents$Companion.class", "size": 2791, "crc": 169508426}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents.class", "size": 4623, "crc": -1122890859}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenDocument.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenDocument.class", "size": 4364, "crc": -1359341656}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenDocumentTree.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenDocumentTree.class", "size": 4333, "crc": 1480534583}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenMultipleDocuments.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenMultipleDocuments.class", "size": 4698, "crc": -740738162}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickContact.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickContact.class", "size": 3280, "crc": -1744271070}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia$Companion.class", "size": 1822, "crc": 83915716}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia.class", "size": 9194, "crc": 937086656}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$Companion.class", "size": 5626, "crc": 1679944831}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageAndVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageAndVideo.class", "size": 1229, "crc": 1211788680}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageOnly.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageOnly.class", "size": 1217, "crc": 711356033}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$SingleMimeType.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$SingleMimeType.class", "size": 1567, "crc": -1904724917}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VideoOnly.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VideoOnly.class", "size": 1217, "crc": -1872246118}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VisualMediaType.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VisualMediaType.class", "size": 1121, "crc": -810064056}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia.class", "size": 9863, "crc": 1159340003}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion.class", "size": 2161, "crc": -1887116569}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions.class", "size": 8429, "crc": -616353483}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestPermission.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestPermission.class", "size": 5298, "crc": 1898056814}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion.class", "size": 1121, "crc": -1011296392}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult.class", "size": 2895, "crc": -733491017}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult$Companion.class", "size": 1241, "crc": 1993673346}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult.class", "size": 3601, "crc": -2089542287}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakePicture.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakePicture.class", "size": 3550, "crc": 1431122998}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakePicturePreview.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakePicturePreview.class", "size": 4112, "crc": -932070601}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakeVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakeVideo.class", "size": 4558, "crc": -61038197}, {"key": "androidx/activity/result/contract/ActivityResultContracts.class", "name": "androidx/activity/result/contract/ActivityResultContracts.class", "size": 2507, "crc": 937485645}, {"key": "META-INF/activity_release.kotlin_module", "name": "META-INF/activity_release.kotlin_module", "size": 393, "crc": 921419579}, {"key": "META-INF/androidx.activity_activity.version", "name": "META-INF/androidx.activity_activity.version", "size": 6, "crc": -1420053471}]