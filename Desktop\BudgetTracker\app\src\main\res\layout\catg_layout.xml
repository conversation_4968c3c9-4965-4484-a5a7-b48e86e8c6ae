<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/catg_layout_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingVertical="10dp"
        android:paddingHorizontal="20dp">

        <LinearLayout
            android:id="@+id/transport"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/transport" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/dark_green"
                android:textSize="20sp"
                android:layout_marginStart="15dp"
                android:text="Transport" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/entertainment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/entertainment" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/dark_green"
                android:layout_marginStart="15dp"
                android:textSize="20sp"
                android:text="Entertainment" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/food"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/food" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/dark_green"
                android:textSize="20sp"
                android:layout_marginStart="15dp"
                android:text="Food" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/housing"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/house" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/dark_green"
                android:textSize="20sp"
                android:text="Housing" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/pet"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/pet" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/dark_green"
                android:textSize="20sp"
                android:layout_marginStart="15dp"
                android:text="Pet" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/health"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/health" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/dark_green"
                android:textSize="20sp"
                android:text="Health" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/shopping"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/shopping" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/dark_green"
                android:layout_marginStart="15dp"
                android:textSize="20sp"
                android:text="Shopping" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/miscellaneous"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/misc" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/dark_green"
                android:textSize="20sp"
                android:text="Miscellaneous" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/savings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/tick" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/montserrat_regular"
                android:textColor="@color/medium_green"
                android:textSize="20sp"
                android:text="💰 Savings" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>