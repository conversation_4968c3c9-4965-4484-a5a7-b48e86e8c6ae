package com.arijit.budgettracker.fragments

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.cardview.widget.CardView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.arijit.budgettracker.R
import com.arijit.budgettracker.SetBudgetActivity
import com.arijit.budgettracker.AddGoalActivity
import com.arijit.budgettracker.models.BudgetViewModel
import com.arijit.budgettracker.utils.BudgetProgressAdapter
import com.arijit.budgettracker.utils.SavingsGoalAdapter
import com.arijit.budgettracker.utils.Vibration

class BudgetFragment : Fragment() {

    private lateinit var viewModel: BudgetViewModel
    private lateinit var budgetProgressAdapter: BudgetProgressAdapter
    private lateinit var savingsGoalAdapter: SavingsGoalAdapter

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_budget, container, false)

        // Initialize ViewModels
        viewModel = ViewModelProvider(this)[BudgetViewModel::class.java]

        // Setup RecyclerViews
        setupBudgetProgressRecyclerView(view)
        setupSavingsGoalsRecyclerView(view)

        // Setup Click Listeners
        setupClickListeners(view)

        // Observe Data
        observeData()

        return view
    }

    private fun setupBudgetProgressRecyclerView(view: View) {
        val budgetProgressRv = view.findViewById<RecyclerView>(R.id.budget_progress_rv)
        budgetProgressAdapter = BudgetProgressAdapter()
        budgetProgressRv.adapter = budgetProgressAdapter
        budgetProgressRv.layoutManager = LinearLayoutManager(requireContext())
    }

    private fun setupSavingsGoalsRecyclerView(view: View) {
        val savingsGoalsRv = view.findViewById<RecyclerView>(R.id.savings_goals_rv)
        savingsGoalAdapter = SavingsGoalAdapter()
        savingsGoalsRv.adapter = savingsGoalAdapter
        savingsGoalsRv.layoutManager = LinearLayoutManager(requireContext())

        // Handle savings goal click for updating progress
        savingsGoalAdapter.onItemClick = { savingsGoal ->
            // TODO: Show dialog to update savings progress
        }
    }

    private fun setupClickListeners(view: View) {
        val setBudgetCard = view.findViewById<CardView>(R.id.set_budget_card)
        val addGoalCard = view.findViewById<CardView>(R.id.add_goal_card)

        setBudgetCard.setOnClickListener {
            Vibration.vibrate(requireContext(), 50)
            startActivity(Intent(requireContext(), SetBudgetActivity::class.java))
        }

        addGoalCard.setOnClickListener {
            Vibration.vibrate(requireContext(), 50)
            startActivity(Intent(requireContext(), AddGoalActivity::class.java))
        }
    }

    private fun observeData() {
        // Observe current month's budgets and expenses for progress calculation
        viewModel.currentMonthBudgetProgress.observe(viewLifecycleOwner) { budgetProgressList ->
            budgetProgressAdapter.submitList(budgetProgressList)
        }

        // Observe active savings goals
        viewModel.activeSavingsGoals.observe(viewLifecycleOwner) { savingsGoals ->
            savingsGoalAdapter.submitList(savingsGoals)
        }
    }

    override fun onResume() {
        super.onResume()
        // Refresh data when returning from other activities
        viewModel.refreshData()
    }
}
