package com.arijit.budgettracker.db

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow

@Dao
interface SavingsGoalDao {
    @Insert
    suspend fun insertSavingsGoal(savingsGoal: SavingsGoal)

    @Update
    suspend fun updateSavingsGoal(savingsGoal: SavingsGoal)

    @Delete
    suspend fun deleteSavingsGoal(savingsGoal: SavingsGoal)

    @Query("SELECT * FROM savings_goals ORDER BY createdDate DESC")
    fun getAllSavingsGoals(): Flow<List<SavingsGoal>>

    @Query("SELECT * FROM savings_goals WHERE isCompleted = 0 ORDER BY targetDate ASC")
    fun getActiveSavingsGoals(): Flow<List<SavingsGoal>>

    @Query("SELECT * FROM savings_goals WHERE isCompleted = 1 ORDER BY createdDate DESC")
    fun getCompletedSavingsGoals(): Flow<List<SavingsGoal>>

    @Query("SELECT * FROM savings_goals WHERE id = :id")
    suspend fun getSavingsGoalById(id: Int): SavingsGoal?

    @Query("UPDATE savings_goals SET currentAmount = :amount WHERE id = :id")
    suspend fun updateSavingsAmount(id: Int, amount: Double)

    @Query("SELECT * FROM savings_goals WHERE isCompleted = 0 ORDER BY targetDate ASC")
    suspend fun getActiveSavingsGoalsList(): List<SavingsGoal>
}
