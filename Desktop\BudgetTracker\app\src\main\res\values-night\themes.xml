<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Dark theme -->
    <style name="Base.Theme.BudgetTracker" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Dark theme colors -->
        <item name="colorPrimary">@color/dark_light_green</item>
        <item name="colorOnPrimary">@color/dark_background</item>
        <item name="colorPrimaryContainer">@color/dark_green_accent</item>
        <item name="colorOnPrimaryContainer">@color/light_text</item>
        <item name="colorSecondary">@color/dark_light_green</item>
        <item name="colorOnSecondary">@color/dark_background</item>
        <item name="colorSecondaryContainer">@color/dark_card</item>
        <item name="colorOnSecondaryContainer">@color/light_text</item>
        <item name="colorTertiary">@color/dark_light_green</item>
        <item name="colorOnTertiary">@color/dark_background</item>
        <item name="colorTertiaryContainer">@color/dark_surface_variant</item>
        <item name="colorOnTertiaryContainer">@color/light_text</item>
        <item name="colorError">@color/red</item>
        <item name="colorOnError">@color/white</item>
        <item name="colorErrorContainer">@color/red</item>
        <item name="colorOnErrorContainer">@color/white</item>
        <item name="colorOutline">@color/dark_green_accent</item>
        <item name="colorOutlineVariant">@color/dark_surface_variant</item>
        <item name="colorSurface">@color/dark_surface</item>
        <item name="colorOnSurface">@color/light_text</item>
        <item name="colorSurfaceVariant">@color/dark_surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/light_text_secondary</item>
        <item name="colorSurfaceContainer">@color/dark_card</item>
        <item name="colorSurfaceContainerHigh">@color/dark_surface_variant</item>
        <item name="colorSurfaceContainerHighest">@color/dark_surface</item>
        <item name="android:colorBackground">@color/dark_background</item>
        <item name="android:textColorPrimary">@color/light_text</item>
        <item name="android:textColorSecondary">@color/light_text_secondary</item>
        <item name="android:windowBackground">@color/dark_background</item>
        <item name="android:statusBarColor">@color/dark_background</item>
        <item name="android:navigationBarColor">@color/dark_background</item>
    </style>
</resources>